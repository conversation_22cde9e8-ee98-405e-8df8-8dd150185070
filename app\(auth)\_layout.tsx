import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';

export default function AuthLayout() {
  return (
    <>
      <StatusBar style="dark" />
      <Stack
        screenOptions={{
          headerShown: false,
          contentStyle: { backgroundColor: '#f8f9fa' },
        }}
      >
        <Stack.Screen 
          name="login" 
          options={{
            title: 'Login',
          }}
        />
        <Stack.Screen 
          name="register" 
          options={{
            title: 'Register',
          }}
        />
        <Stack.Screen 
          name="forgot-password" 
          options={{
            title: 'Forgot Password',
          }}
        />
      </Stack>
    </>
  );
}
