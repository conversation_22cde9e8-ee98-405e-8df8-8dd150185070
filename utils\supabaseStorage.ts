import {
    combinedService,
    DatabaseAppSettings,
    DatabaseSetoranHistory,
    DatabaseTabunganData,
    DatabaseTarget,
    DatabaseUser,
    getCurrentUserId,
    setoranService,
    settingsService,
    tabunganService,
    targetService,
    userService
} from './supabase';

// Import types from original storage for compatibility
import { AppSettings, SetoranItem, TabunganData, TargetData, UserData } from './storage';

// Helper functions to convert between database and app types
const convertDatabaseUserToUserData = (dbUser: DatabaseUser): UserData => ({
  nama: dbUser.nama,
  email: dbUser.email,
  telepon: dbUser.telepon || '',
  alamat: dbUser.alamat || '',
  tanggalLahir: dbUser.tanggal_lahir,
});

const convertUserDataToDatabaseUser = (userData: UserData): Omit<DatabaseUser, 'id' | 'created_at' | 'updated_at'> => ({
  nama: userData.nama,
  email: userData.email,
  telepon: userData.telepon,
  alamat: userData.alamat,
  tanggal_lahir: userData.tanggalLahir,
});

const convertDatabaseTargetToTargetData = (dbTarget: DatabaseTarget): TargetData => ({
  targetBiaya: dbTarget.target_biaya,
  tanggalTarget: dbTarget.tanggal_target,
  paketHaji: dbTarget.paket_haji,
});

const convertTargetDataToDatabaseTarget = (targetData: TargetData, userId: string): Omit<DatabaseTarget, 'id' | 'created_at' | 'updated_at'> => ({
  user_id: userId,
  target_biaya: targetData.targetBiaya,
  tanggal_target: targetData.tanggalTarget,
  paket_haji: targetData.paketHaji,
});

const convertDatabaseSetoranToSetoranItem = (dbSetoran: DatabaseSetoranHistory): SetoranItem => ({
  id: parseInt(dbSetoran.id.replace(/-/g, '').substring(0, 10), 16), // Convert UUID to number for compatibility
  tanggal: dbSetoran.tanggal,
  jumlah: dbSetoran.jumlah,
  keterangan: dbSetoran.keterangan || '',
});

const convertSetoranItemToDatabaseSetoran = (setoran: Omit<SetoranItem, 'id'>, userId: string): Omit<DatabaseSetoranHistory, 'id' | 'created_at'> => ({
  user_id: userId,
  tanggal: setoran.tanggal,
  jumlah: setoran.jumlah,
  keterangan: setoran.keterangan,
});

const convertDatabaseTabunganToTabunganData = (dbTabungan: DatabaseTabunganData, setoranHistory: DatabaseSetoranHistory[]): TabunganData => ({
  totalTabungan: dbTabungan.total_tabungan,
  riwayatSetoran: setoranHistory.map(convertDatabaseSetoranToSetoranItem),
  lastUpdated: dbTabungan.last_updated,
});

const convertDatabaseSettingsToAppSettings = (dbSettings: DatabaseAppSettings): AppSettings => ({
  theme: dbSettings.theme,
  notifications: dbSettings.notifications,
  language: dbSettings.language,
});

// User storage functions (compatible with original interface)
export const userStorage = {
  async saveUserData(userData: UserData): Promise<void> {
    const userId = getCurrentUserId();
    
    try {
      // Check if user exists
      const existingUser = await userService.getUserById(userId);
      
      if (existingUser) {
        // Update existing user
        await userService.updateUser(userId, convertUserDataToDatabaseUser(userData));
      } else {
        // Create new user with fixed ID (for demo purposes)
        // In production, this would be handled by authentication
        throw new Error('User not found. Please initialize user first.');
      }
    } catch (error) {
      console.error('Error saving user data:', error);
      throw error;
    }
  },

  async getUserData(): Promise<UserData | null> {
    const userId = getCurrentUserId();

    try {
      const dbUser = await userService.getUserById(userId);
      return dbUser ? convertDatabaseUserToUserData(dbUser) : null;
    } catch (error) {
      // Enhanced error handling with proper categorization
      const { handleSupabaseError } = await import('./errorHandling');
      const appError = handleSupabaseError(error, 'get user data', { userId });
      console.error('Error getting user data:', appError);
      throw appError;
    }
  },
};

// Target storage functions (compatible with original interface)
export const targetStorage = {
  async saveTargetData(targetData: TargetData): Promise<void> {
    const userId = getCurrentUserId();
    
    try {
      // Check if target exists
      const existingTarget = await targetService.getTargetByUserId(userId);
      
      if (existingTarget) {
        // Update existing target
        await targetService.updateTarget(userId, {
          target_biaya: targetData.targetBiaya,
          tanggal_target: targetData.tanggalTarget,
          paket_haji: targetData.paketHaji,
        });
      } else {
        // Create new target
        await targetService.createTarget(convertTargetDataToDatabaseTarget(targetData, userId));
      }
    } catch (error) {
      console.error('Error saving target data:', error);
      throw error;
    }
  },

  async getTargetData(): Promise<TargetData | null> {
    const userId = getCurrentUserId();
    
    try {
      const dbTarget = await targetService.getTargetByUserId(userId);
      return dbTarget ? convertDatabaseTargetToTargetData(dbTarget) : null;
    } catch (error) {
      console.error('Error getting target data:', error);
      return null;
    }
  },
};

// Tabungan storage functions (compatible with original interface)
export const tabunganStorage = {
  async saveTabunganData(tabunganData: TabunganData): Promise<void> {
    const userId = getCurrentUserId();
    
    try {
      // Update tabungan data (total is automatically calculated by database triggers)
      await tabunganService.updateTabunganData(userId, {
        last_updated: tabunganData.lastUpdated,
      });
    } catch (error) {
      console.error('Error saving tabungan data:', error);
      throw error;
    }
  },

  async getTabunganData(): Promise<TabunganData | null> {
    const userId = getCurrentUserId();
    
    try {
      const [dbTabungan, setoranHistory] = await Promise.all([
        tabunganService.getTabunganDataByUserId(userId),
        setoranService.getSetoranHistoryByUserId(userId),
      ]);
      
      if (!dbTabungan) {
        return null;
      }
      
      return convertDatabaseTabunganToTabunganData(dbTabungan, setoranHistory);
    } catch (error) {
      console.error('Error getting tabungan data:', error);
      return null;
    }
  },

  async addSetoran(setoran: Omit<SetoranItem, 'id'>): Promise<void> {
    const userId = getCurrentUserId();
    
    try {
      // Add setoran to database (total will be automatically updated by triggers)
      await setoranService.addSetoran(convertSetoranItemToDatabaseSetoran(setoran, userId));
    } catch (error) {
      console.error('Error adding setoran:', error);
      throw error;
    }
  },
};

// Settings storage functions (compatible with original interface)
export const settingsStorage = {
  async saveSettings(settings: AppSettings): Promise<void> {
    const userId = getCurrentUserId();
    
    try {
      // Check if settings exist
      const existingSettings = await settingsService.getSettingsByUserId(userId);
      
      if (existingSettings) {
        // Update existing settings
        await settingsService.updateSettings(userId, settings);
      } else {
        // Create new settings
        await settingsService.createSettings(userId, settings);
      }
    } catch (error) {
      console.error('Error saving settings:', error);
      throw error;
    }
  },

  async getSettings(): Promise<AppSettings | null> {
    const userId = getCurrentUserId();
    
    try {
      const dbSettings = await settingsService.getSettingsByUserId(userId);
      return dbSettings ? convertDatabaseSettingsToAppSettings(dbSettings) : null;
    } catch (error) {
      console.error('Error getting settings:', error);
      return null;
    }
  },
};

// Initialize user data (helper function for migration)
export const initializeUserData = async (userData: UserData, targetData?: TargetData): Promise<void> => {
  try {
    const result = await combinedService.initializeUser(
      convertUserDataToDatabaseUser(userData),
      targetData ? {
        target_biaya: targetData.targetBiaya,
        tanggal_target: targetData.tanggalTarget,
        paket_haji: targetData.paketHaji,
      } : undefined
    );
    
    console.log('User initialized successfully:', result.user.id);
  } catch (error) {
    console.error('Error initializing user:', error);
    throw error;
  }
};
