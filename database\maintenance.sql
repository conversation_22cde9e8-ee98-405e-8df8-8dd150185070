-- Tabunga<PERSON> Haji - Database Maintenance and Utility Queries
-- This file contains useful queries for database maintenance, monitoring, and troubleshooting

-- =============================================
-- DATABASE HEALTH CHECKS
-- =============================================

-- Check table sizes and row counts
SELECT 
    schemaname,
    tablename,
    attname,
    n_distinct,
    correlation
FROM pg_stats 
WHERE schemaname = 'public' 
    AND tablename IN ('users', 'targets', 'tabungan_data', 'setoran_history', 'app_settings')
ORDER BY tablename, attname;

-- Check for orphaned records
SELECT 'Orphaned targets' as issue, COUNT(*) as count
FROM targets t 
LEFT JOIN users u ON t.user_id = u.id 
WHERE u.id IS NULL

UNION ALL

SELECT 'Orphaned tabungan_data' as issue, COUNT(*) as count
FROM tabungan_data td 
LEFT JOIN users u ON td.user_id = u.id 
WHERE u.id IS NULL

UNION ALL

SELECT 'Orphaned setoran_history' as issue, COUNT(*) as count
FROM setoran_history sh 
LEFT JOIN users u ON sh.user_id = u.id 
WHERE u.id IS NULL

UNION ALL

SELECT 'Orphaned app_settings' as issue, COUNT(*) as count
FROM app_settings s 
LEFT JOIN users u ON s.user_id = u.id 
WHERE u.id IS NULL;

-- =============================================
-- USER STATISTICS
-- =============================================

-- Overall user statistics
SELECT 
    COUNT(*) as total_users,
    COUNT(CASE WHEN created_at >= CURRENT_DATE - INTERVAL '7 days' THEN 1 END) as new_users_7d,
    COUNT(CASE WHEN created_at >= CURRENT_DATE - INTERVAL '30 days' THEN 1 END) as new_users_30d,
    COUNT(CASE WHEN nama IS NOT NULL AND nama != '' THEN 1 END) as users_with_names,
    COUNT(CASE WHEN telepon IS NOT NULL AND telepon != '' THEN 1 END) as users_with_phone
FROM users;

-- User engagement statistics
SELECT 
    COUNT(DISTINCT u.id) as total_users,
    COUNT(DISTINCT t.user_id) as users_with_targets,
    COUNT(DISTINCT sh.user_id) as users_with_deposits,
    COUNT(DISTINCT CASE WHEN sh.created_at >= CURRENT_DATE - INTERVAL '30 days' THEN sh.user_id END) as active_users_30d,
    ROUND(
        COUNT(DISTINCT CASE WHEN sh.created_at >= CURRENT_DATE - INTERVAL '30 days' THEN sh.user_id END)::DECIMAL / 
        COUNT(DISTINCT u.id)::DECIMAL * 100, 2
    ) as engagement_rate_30d
FROM users u
LEFT JOIN targets t ON u.id = t.user_id
LEFT JOIN setoran_history sh ON u.id = sh.user_id;

-- =============================================
-- SAVINGS STATISTICS
-- =============================================

-- Savings overview
SELECT 
    COUNT(DISTINCT td.user_id) as users_with_savings,
    SUM(td.total_tabungan) as total_savings_amount,
    AVG(td.total_tabungan) as avg_savings_per_user,
    MIN(td.total_tabungan) as min_savings,
    MAX(td.total_tabungan) as max_savings,
    PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY td.total_tabungan) as median_savings
FROM tabungan_data td
WHERE td.total_tabungan > 0;

-- Target achievement statistics
SELECT 
    COUNT(*) as total_targets,
    COUNT(CASE WHEN td.total_tabungan >= t.target_biaya THEN 1 END) as targets_achieved,
    ROUND(
        COUNT(CASE WHEN td.total_tabungan >= t.target_biaya THEN 1 END)::DECIMAL / 
        COUNT(*)::DECIMAL * 100, 2
    ) as achievement_rate,
    AVG(
        CASE WHEN t.target_biaya > 0 THEN 
            (td.total_tabungan::DECIMAL / t.target_biaya::DECIMAL) * 100 
        ELSE 0 END
    ) as avg_progress_percentage
FROM targets t
JOIN tabungan_data td ON t.user_id = td.user_id;

-- Hajj package distribution
SELECT 
    paket_haji,
    COUNT(*) as count,
    ROUND(COUNT(*)::DECIMAL / (SELECT COUNT(*) FROM targets)::DECIMAL * 100, 2) as percentage,
    AVG(target_biaya) as avg_target_amount,
    AVG(td.total_tabungan) as avg_current_savings
FROM targets t
JOIN tabungan_data td ON t.user_id = td.user_id
GROUP BY paket_haji
ORDER BY count DESC;

-- =============================================
-- DEPOSIT ANALYSIS
-- =============================================

-- Deposit frequency analysis
SELECT 
    DATE_TRUNC('month', tanggal) as month,
    COUNT(*) as total_deposits,
    COUNT(DISTINCT user_id) as unique_users,
    SUM(jumlah) as total_amount,
    AVG(jumlah) as avg_deposit_amount
FROM setoran_history
WHERE tanggal >= CURRENT_DATE - INTERVAL '12 months'
GROUP BY DATE_TRUNC('month', tanggal)
ORDER BY month DESC;

-- Top depositors
SELECT 
    u.nama,
    u.email,
    COUNT(sh.id) as total_deposits,
    SUM(sh.jumlah) as total_amount,
    AVG(sh.jumlah) as avg_deposit,
    MAX(sh.tanggal) as last_deposit_date
FROM users u
JOIN setoran_history sh ON u.id = sh.user_id
GROUP BY u.id, u.nama, u.email
ORDER BY total_amount DESC
LIMIT 10;

-- Deposit patterns by day of week
SELECT 
    EXTRACT(DOW FROM tanggal) as day_of_week,
    CASE EXTRACT(DOW FROM tanggal)
        WHEN 0 THEN 'Sunday'
        WHEN 1 THEN 'Monday'
        WHEN 2 THEN 'Tuesday'
        WHEN 3 THEN 'Wednesday'
        WHEN 4 THEN 'Thursday'
        WHEN 5 THEN 'Friday'
        WHEN 6 THEN 'Saturday'
    END as day_name,
    COUNT(*) as deposit_count,
    SUM(jumlah) as total_amount,
    AVG(jumlah) as avg_amount
FROM setoran_history
GROUP BY EXTRACT(DOW FROM tanggal)
ORDER BY day_of_week;

-- =============================================
-- DATA INTEGRITY CHECKS
-- =============================================

-- Check for inconsistent totals
SELECT 
    u.nama,
    u.email,
    td.total_tabungan as recorded_total,
    COALESCE(SUM(sh.jumlah), 0) as calculated_total,
    td.total_tabungan - COALESCE(SUM(sh.jumlah), 0) as difference
FROM users u
JOIN tabungan_data td ON u.id = td.user_id
LEFT JOIN setoran_history sh ON u.id = sh.user_id
GROUP BY u.id, u.nama, u.email, td.total_tabungan
HAVING td.total_tabungan != COALESCE(SUM(sh.jumlah), 0)
ORDER BY ABS(td.total_tabungan - COALESCE(SUM(sh.jumlah), 0)) DESC;

-- Check for future dates in deposits
SELECT 
    u.nama,
    u.email,
    sh.tanggal,
    sh.jumlah,
    sh.keterangan
FROM users u
JOIN setoran_history sh ON u.id = sh.user_id
WHERE sh.tanggal > CURRENT_DATE
ORDER BY sh.tanggal DESC;

-- Check for negative amounts
SELECT 
    u.nama,
    u.email,
    sh.tanggal,
    sh.jumlah,
    sh.keterangan
FROM users u
JOIN setoran_history sh ON u.id = sh.user_id
WHERE sh.jumlah <= 0
ORDER BY sh.tanggal DESC;

-- =============================================
-- MAINTENANCE OPERATIONS
-- =============================================

-- Recalculate all tabungan totals (use with caution)
-- UPDATE tabungan_data 
-- SET total_tabungan = (
--     SELECT COALESCE(SUM(jumlah), 0) 
--     FROM setoran_history 
--     WHERE user_id = tabungan_data.user_id
-- ),
-- last_updated = NOW(),
-- updated_at = NOW();

-- Clean up orphaned records (use with extreme caution)
-- DELETE FROM targets WHERE user_id NOT IN (SELECT id FROM users);
-- DELETE FROM tabungan_data WHERE user_id NOT IN (SELECT id FROM users);
-- DELETE FROM setoran_history WHERE user_id NOT IN (SELECT id FROM users);
-- DELETE FROM app_settings WHERE user_id NOT IN (SELECT id FROM users);

-- =============================================
-- PERFORMANCE MONITORING
-- =============================================

-- Check index usage
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_scan as index_scans,
    idx_tup_read as tuples_read,
    idx_tup_fetch as tuples_fetched
FROM pg_stat_user_indexes 
WHERE schemaname = 'public'
ORDER BY idx_scan DESC;

-- Check table access patterns
SELECT 
    schemaname,
    tablename,
    seq_scan as sequential_scans,
    seq_tup_read as sequential_tuples_read,
    idx_scan as index_scans,
    idx_tup_fetch as index_tuples_fetched,
    n_tup_ins as inserts,
    n_tup_upd as updates,
    n_tup_del as deletes
FROM pg_stat_user_tables 
WHERE schemaname = 'public'
ORDER BY seq_scan + idx_scan DESC;

-- =============================================
-- BACKUP VERIFICATION
-- =============================================

-- Generate data export for backup verification
-- SELECT 'users' as table_name, COUNT(*) as row_count FROM users
-- UNION ALL
-- SELECT 'targets' as table_name, COUNT(*) as row_count FROM targets
-- UNION ALL
-- SELECT 'tabungan_data' as table_name, COUNT(*) as row_count FROM tabungan_data
-- UNION ALL
-- SELECT 'setoran_history' as table_name, COUNT(*) as row_count FROM setoran_history
-- UNION ALL
-- SELECT 'app_settings' as table_name, COUNT(*) as row_count FROM app_settings;

-- =============================================
-- USAGE NOTES
-- =============================================

-- To use these queries:
-- 1. Copy the desired query
-- 2. Paste into Supabase SQL Editor
-- 3. Execute to get results
-- 4. Use results for monitoring and maintenance

-- For maintenance operations (commented out):
-- 1. Uncomment carefully
-- 2. Test on development first
-- 3. Backup data before running
-- 4. Monitor results closely
