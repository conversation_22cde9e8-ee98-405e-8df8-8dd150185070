import React, { useEffect } from 'react';
import { View, ViewProps, StyleSheet } from 'react-native';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withTiming,
  withDelay,
  withSpring,
  Easing,
} from 'react-native-reanimated';

interface AnimatedProgressBarProps extends ViewProps {
  progress: number; // 0 to 100
  height?: number;
  backgroundColor?: string;
  progressColor?: string;
  borderRadius?: number;
  animationDelay?: number;
  animationDuration?: number;
  springAnimation?: boolean;
}

export function AnimatedProgressBar({
  progress,
  height = 8,
  backgroundColor = 'rgba(255,255,255,0.3)',
  progressColor = 'white',
  borderRadius = 4,
  animationDelay = 0,
  animationDuration = 1000,
  springAnimation = true,
  style,
  ...props
}: AnimatedProgressBarProps) {
  const progressWidth = useSharedValue(0);
  const progressOpacity = useSharedValue(0);

  const containerStyle = {
    height,
    backgroundColor,
    borderRadius,
    overflow: 'hidden' as const,
  };

  const progressStyle = useAnimatedStyle(() => {
    return {
      width: `${progressWidth.value}%`,
      opacity: progressOpacity.value,
    };
  });

  useEffect(() => {
    const clampedProgress = Math.min(Math.max(progress, 0), 100);
    
    // First fade in the container
    progressOpacity.value = withDelay(
      animationDelay,
      withTiming(1, { duration: 300 })
    );

    // Then animate the progress
    if (springAnimation) {
      progressWidth.value = withDelay(
        animationDelay + 200,
        withSpring(clampedProgress, {
          damping: 15,
          stiffness: 100,
          mass: 1,
        })
      );
    } else {
      progressWidth.value = withDelay(
        animationDelay + 200,
        withTiming(clampedProgress, {
          duration: animationDuration,
          easing: Easing.out(Easing.cubic),
        })
      );
    }
  }, [progress, animationDelay, animationDuration, springAnimation]);

  return (
    <View style={[containerStyle, style]} {...props}>
      <Animated.View
        style={[
          styles.progressFill,
          {
            backgroundColor: progressColor,
            borderRadius,
          },
          progressStyle,
        ]}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  progressFill: {
    height: '100%',
  },
});

// Convenience component for circular progress
export function AnimatedCircularProgress({
  progress,
  size = 60,
  strokeWidth = 6,
  backgroundColor = 'rgba(255,255,255,0.3)',
  progressColor = 'white',
  animationDelay = 0,
  animationDuration = 1000,
}: {
  progress: number;
  size?: number;
  strokeWidth?: number;
  backgroundColor?: string;
  progressColor?: string;
  animationDelay?: number;
  animationDuration?: number;
}) {
  const progressValue = useSharedValue(0);
  
  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ rotate: `${progressValue.value * 3.6}deg` }],
    };
  });

  useEffect(() => {
    const clampedProgress = Math.min(Math.max(progress, 0), 100);
    
    progressValue.value = withDelay(
      animationDelay,
      withSpring(clampedProgress, {
        damping: 15,
        stiffness: 100,
      })
    );
  }, [progress, animationDelay]);

  const radius = (size - strokeWidth) / 2;
  const circumference = 2 * Math.PI * radius;

  return (
    <View style={{ width: size, height: size }}>
      {/* Background circle */}
      <View
        style={{
          position: 'absolute',
          width: size,
          height: size,
          borderRadius: size / 2,
          borderWidth: strokeWidth,
          borderColor: backgroundColor,
        }}
      />
      
      {/* Progress circle */}
      <Animated.View
        style={[
          {
            position: 'absolute',
            width: size,
            height: size,
            borderRadius: size / 2,
            borderWidth: strokeWidth,
            borderColor: progressColor,
            borderTopColor: 'transparent',
            borderRightColor: 'transparent',
            borderBottomColor: 'transparent',
          },
          animatedStyle,
        ]}
      />
    </View>
  );
}
