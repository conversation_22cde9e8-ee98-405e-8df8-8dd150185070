import { useAuth } from '@/contexts/AuthContext';
import { Redirect } from 'expo-router';
import React, { ReactNode } from 'react';
import { ActivityIndicator, StyleSheet, View } from 'react-native';

interface ProtectedRouteProps {
  children: ReactNode;
  fallback?: ReactNode;
}

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ 
  children, 
  fallback 
}) => {
  const { user, loading } = useAuth();

  // Show loading spinner while checking authentication
  if (loading) {
    return (
      <View style={styles.container}>
        <ActivityIndicator size="large" color="#194a7a" testID="loading-indicator" />
      </View>
    );
  }

  // If not authenticated, redirect to login or show fallback
  if (!user) {
    if (fallback) {
      return <>{fallback}</>;
    }
    return <Redirect href="/(auth)/login" />;
  }

  // If authenticated, render children
  return <>{children}</>;
};

// Hook for components that need to ensure authentication
export const useProtectedRoute = () => {
  const { user, loading } = useAuth();

  if (loading) {
    return { user: null, loading: true, isAuthenticated: false };
  }

  if (!user) {
    return { user: null, loading: false, isAuthenticated: false };
  }

  return { user, loading: false, isAuthenticated: true };
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
  },
});
