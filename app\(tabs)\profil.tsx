import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Colors';
import { useAuth } from '@/contexts/AuthContext';
import { useLogout, usePasswordUpdate } from '@/hooks/useAuth';
import { useColorScheme } from '@/hooks/useColorScheme';
import { DatabaseTarget, DatabaseUser } from '@/utils/supabase';
import React, { useState } from 'react';
import {
    Alert,
    Modal,
    ScrollView,
    StyleSheet,
    Text,
    TextInput,
    TouchableOpacity,
    View
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

export default function ProfilScreen() {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];
  const { user } = useAuth();
  const { logout, loading: logoutLoading } = useLogout();
  const { changePassword, loading: passwordLoading, success: passwordSuccess, clearSuccess } = usePasswordUpdate();

  const [userData, setUserData] = useState<DatabaseUser | null>(null);
  const [targetData, setTargetData] = useState<DatabaseTarget | null>(null);
  const [loading, setLoading] = useState(true);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [targetModalVisible, setTargetModalVisible] = useState(false);
  const [passwordModalVisible, setPasswordModalVisible] = useState(false);
  const [editData, setEditData] = useState({
    nama: '',
    email: '',
    telepon: '',
    alamat: '',
    tanggal_lahir: '',
  });
  const [editTargetData, setEditTargetData] = useState({
    target_biaya: '',
    tanggal_target: '',
    paket_haji: 'reguler' as 'reguler' | 'plus' | 'khusus',
  });
  const [passwordData, setPasswordData] = useState({
    newPassword: '',
    confirmPassword: '',
  });

  // Load user data on component mount
  useEffect(() => {
    loadUserData();
  }, []);

  const loadUserData = async () => {
    try {
      setLoading(true);
      const [profile, target] = await Promise.all([
        authUserService.getCurrentUserProfile(),
        authUserService.getCurrentUserTarget(),
      ]);

      setUserData(profile);
      setTargetData(target);

      if (profile) {
        setEditData({
          nama: profile.nama,
          email: profile.email,
          telepon: profile.telepon,
          alamat: profile.alamat,
          tanggal_lahir: profile.tanggal_lahir,
        });
      }

      if (target) {
        setEditTargetData({
          target_biaya: target.target_biaya.toString(),
          tanggal_target: target.tanggal_target,
          paket_haji: target.paket_haji,
        });
      }
    } catch (error) {
      console.error('Error loading user data:', error);
      Alert.alert('Error', 'Gagal memuat data profil');
    } finally {
      setLoading(false);
    }
  };

  const formatRupiah = (amount: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const formatTanggal = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('id-ID', {
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    });
  };

  const handleSaveProfile = async () => {
    try {
      await authUserService.upsertCurrentUserProfile(editData);
      await loadUserData(); // Reload data
      setEditModalVisible(false);
      Alert.alert('Berhasil', 'Profil berhasil diperbarui');
    } catch (error) {
      console.error('Error saving profile:', error);
      Alert.alert('Error', 'Gagal menyimpan profil');
    }
  };

  const handleSaveTarget = async () => {
    const targetBiaya = Number(editTargetData.target_biaya);
    if (targetBiaya <= 0) {
      Alert.alert('Error', 'Target haji harus lebih dari 0');
      return;
    }

    try {
      await authUserService.upsertCurrentUserTarget({
        target_biaya: targetBiaya,
        tanggal_target: editTargetData.tanggal_target,
        paket_haji: editTargetData.paket_haji,
      });
      await loadUserData(); // Reload data
      setTargetModalVisible(false);
      Alert.alert('Berhasil', 'Target haji berhasil diperbarui');
    } catch (error) {
      console.error('Error saving target:', error);
      Alert.alert('Error', 'Gagal menyimpan target');
    }
  };

  const handleChangePassword = async () => {
    if (passwordData.newPassword.length < 6) {
      Alert.alert('Error', 'Password harus minimal 6 karakter');
      return;
    }

    if (passwordData.newPassword !== passwordData.confirmPassword) {
      Alert.alert('Error', 'Konfirmasi password tidak cocok');
      return;
    }

    try {
      await changePassword(passwordData.newPassword);
      setPasswordModalVisible(false);
      setPasswordData({ newPassword: '', confirmPassword: '' });
      Alert.alert('Berhasil', 'Password berhasil diubah');
    } catch (error) {
      console.error('Error changing password:', error);
      Alert.alert('Error', 'Gagal mengubah password');
    }
  };

  const handleLogout = () => {
    Alert.alert(
      'Logout',
      'Apakah Anda yakin ingin keluar dari aplikasi?',
      [
        { text: 'Batal', style: 'cancel' },
        {
          text: 'Logout',
          style: 'destructive',
          onPress: async () => {
            try {
              await logout();
            } catch (error) {
              console.error('Error logging out:', error);
              Alert.alert('Error', 'Gagal logout');
            }
          }
        }
      ]
    );
  };

  const menuItems = [
    {
      icon: 'person.fill',
      title: 'Edit Profil',
      subtitle: 'Ubah informasi pribadi',
      onPress: () => {
        if (userData) {
          setEditData({
            nama: userData.nama,
            email: userData.email,
            telepon: userData.telepon,
            alamat: userData.alamat,
            tanggal_lahir: userData.tanggal_lahir,
          });
        }
        setEditModalVisible(true);
      }
    },
    {
      icon: 'target',
      title: 'Edit Target',
      subtitle: 'Ubah target tabungan haji',
      onPress: () => {
        if (targetData) {
          setEditTargetData({
            target_biaya: targetData.target_biaya.toString(),
            tanggal_target: targetData.tanggal_target,
            paket_haji: targetData.paket_haji,
          });
        }
        setTargetModalVisible(true);
      }
    },
    {
      icon: 'key.fill',
      title: 'Ubah Password',
      subtitle: 'Ganti password akun',
      onPress: () => {
        setPasswordData({ newPassword: '', confirmPassword: '' });
        setPasswordModalVisible(true);
      }
    },
  ];

  // Show loading spinner while loading data
  if (loading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={[styles.loadingText, { color: colors.text }]}>
            Memuat data profil...
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={[styles.title, { color: colors.text }]}>
            Profil
          </Text>
        </View>

        {/* Profile Card */}
        <View style={[styles.profileCard, { backgroundColor: colors.primary }]}>
          <View style={styles.avatarContainer}>
            <View style={styles.avatar}>
              <IconSymbol name="person.fill" size={32} color="white" />
            </View>
          </View>
          <Text style={styles.profileName}>
            {userData?.nama || user?.email?.split('@')[0] || 'User'}
          </Text>
          <Text style={styles.profileEmail}>
            {userData?.email || user?.email || 'No email'}
          </Text>
        </View>

        {/* Info Cards */}
        <View style={styles.section}>
          {targetData && (
            <>
              <View style={[styles.infoCard, { backgroundColor: colors.cardBackground }]}>
                <IconSymbol name="target" size={20} color={colors.primary} />
                <View style={styles.infoContent}>
                  <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>
                    Target Haji
                  </Text>
                  <Text style={[styles.infoValue, { color: colors.primary }]}>
                    {formatRupiah(targetData.target_biaya)}
                  </Text>
                </View>
              </View>

              <View style={[styles.infoCard, { backgroundColor: colors.cardBackground }]}>
                <IconSymbol name="calendar" size={20} color={colors.primary} />
                <View style={styles.infoContent}>
                  <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>
                    Target Tanggal
                  </Text>
                  <Text style={[styles.infoValue, { color: colors.primary }]}>
                    {formatTanggal(targetData.tanggal_target)}
                  </Text>
                </View>
              </View>

              <View style={[styles.infoCard, { backgroundColor: colors.cardBackground }]}>
                <IconSymbol name="star.fill" size={20} color={colors.primary} />
                <View style={styles.infoContent}>
                  <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>
                    Paket Haji
                  </Text>
                  <Text style={[styles.infoValue, { color: colors.primary }]}>
                    {targetData.paket_haji.charAt(0).toUpperCase() + targetData.paket_haji.slice(1)}
                  </Text>
                </View>
              </View>
            </>
          )}

          {!targetData && (
            <View style={[styles.infoCard, { backgroundColor: colors.cardBackground }]}>
              <IconSymbol name="exclamationmark.triangle" size={20} color={colors.textSecondary} />
              <View style={styles.infoContent}>
                <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>
                  Target Haji
                </Text>
                <Text style={[styles.infoValue, { color: colors.textSecondary }]}>
                  Belum diatur
                </Text>
              </View>
            </View>
          )}
        </View>

        {/* Menu Items */}
        <View style={styles.section}>
          {menuItems.map((item, index) => (
            <TouchableOpacity
              key={index}
              style={[styles.menuItem, { backgroundColor: colors.cardBackground }]}
              onPress={item.onPress}
            >
              <View style={styles.menuLeft}>
                <View style={[styles.menuIcon, { backgroundColor: colors.backgroundTertiary }]}>
                  <IconSymbol name={item.icon as any} size={20} color={colors.primary} />
                </View>
                <View>
                  <Text style={[styles.menuTitle, { color: colors.text }]}>
                    {item.title}
                  </Text>
                  <Text style={[styles.menuSubtitle, { color: colors.textSecondary }]}>
                    {item.subtitle}
                  </Text>
                </View>
              </View>
              <IconSymbol name="chevron.right" size={16} color={colors.textSecondary} />
            </TouchableOpacity>
          ))}
        </View>

        {/* Logout Button */}
        <View style={styles.section}>
          <TouchableOpacity
            style={[styles.logoutButton, {
              backgroundColor: colors.cardBackground,
              borderWidth: 1,
              borderColor: colors.error
            }]}
            onPress={handleLogout}
          >
            <Text style={[styles.logoutText, { color: colors.error }]}>Logout</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>

      {/* Edit Profile Modal */}
      <Modal
        animationType="slide"
        transparent={true}
        visible={editModalVisible}
        onRequestClose={() => setEditModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContent, { backgroundColor: colors.background }]}>
            <View style={styles.modalHeader}>
              <Text style={[styles.modalTitle, { color: colors.text }]}>
                Edit Profil
              </Text>
              <TouchableOpacity 
                onPress={() => setEditModalVisible(false)}
                style={styles.closeButton}
              >
                <Text style={[styles.closeButtonText, { color: colors.text }]}>✕</Text>
              </TouchableOpacity>
            </View>

            <ScrollView>
              <View style={styles.inputContainer}>
                <Text style={[styles.inputLabel, { color: colors.text }]}>Nama</Text>
                <TextInput
                  style={[styles.input, {
                    borderColor: colors.secondary,
                    color: colors.text,
                    backgroundColor: colors.backgroundSecondary
                  }]}
                  value={editData.nama}
                  onChangeText={(text) => setEditData({...editData, nama: text})}
                />
              </View>

              <View style={styles.inputContainer}>
                <Text style={[styles.inputLabel, { color: colors.text }]}>Email</Text>
                <TextInput
                  style={[styles.input, { borderColor: colors.tint, color: colors.text }]}
                  value={editData.email}
                  onChangeText={(text) => setEditData({...editData, email: text})}
                  keyboardType="email-address"
                />
              </View>

              <View style={styles.inputContainer}>
                <Text style={[styles.inputLabel, { color: colors.text }]}>Telepon</Text>
                <TextInput
                  style={[styles.input, { borderColor: colors.tint, color: colors.text }]}
                  value={editData.telepon}
                  onChangeText={(text) => setEditData({...editData, telepon: text})}
                  keyboardType="phone-pad"
                />
              </View>

              <View style={styles.inputContainer}>
                <Text style={[styles.inputLabel, { color: colors.text }]}>Alamat</Text>
                <TextInput
                  style={[styles.input, { borderColor: colors.tint, color: colors.text }]}
                  value={editData.alamat}
                  onChangeText={(text) => setEditData({...editData, alamat: text})}
                  multiline
                  numberOfLines={3}
                />
              </View>
            </ScrollView>

            <TouchableOpacity
              style={[styles.submitButton, { backgroundColor: colors.primary }]}
              onPress={handleSaveProfile}
            >
              <Text style={styles.submitButtonText}>Simpan Perubahan</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>

      {/* Edit Target Modal */}
      <Modal
        animationType="slide"
        transparent={true}
        visible={targetModalVisible}
        onRequestClose={() => setTargetModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContent, { backgroundColor: colors.background }]}>
            <View style={styles.modalHeader}>
              <Text style={[styles.modalTitle, { color: colors.text }]}>
                Edit Target Haji
              </Text>
              <TouchableOpacity 
                onPress={() => setTargetModalVisible(false)}
                style={styles.closeButton}
              >
                <Text style={[styles.closeButtonText, { color: colors.text }]}>✕</Text>
              </TouchableOpacity>
            </View>

            <View style={styles.inputContainer}>
              <Text style={[styles.inputLabel, { color: colors.text }]}>
                Target Biaya Haji (Rp)
              </Text>
              <TextInput
                style={[styles.input, { borderColor: colors.tint, color: colors.text }]}
                value={targetData.targetHaji}
                onChangeText={(text) => setTargetData({...targetData, targetHaji: text})}
                keyboardType="numeric"
                placeholder="35000000"
              />
            </View>

            <View style={styles.inputContainer}>
              <Text style={[styles.inputLabel, { color: colors.text }]}>
                Target Tanggal (YYYY-MM-DD)
              </Text>
              <TextInput
                style={[styles.input, { borderColor: colors.tint, color: colors.text }]}
                value={targetData.tanggalTarget}
                onChangeText={(text) => setTargetData({...targetData, tanggalTarget: text})}
                placeholder="2026-12-31"
              />
            </View>

            <TouchableOpacity
              style={[styles.submitButton, { backgroundColor: colors.primary }]}
              onPress={handleSaveTarget}
            >
              <Text style={styles.submitButtonText}>Simpan Target</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>

      {/* Change Password Modal */}
      <Modal
        animationType="slide"
        transparent={true}
        visible={passwordModalVisible}
        onRequestClose={() => setPasswordModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContent, { backgroundColor: colors.background }]}>
            <View style={styles.modalHeader}>
              <Text style={[styles.modalTitle, { color: colors.text }]}>
                Ubah Password
              </Text>
              <TouchableOpacity
                onPress={() => setPasswordModalVisible(false)}
                style={styles.closeButton}
              >
                <Text style={[styles.closeButtonText, { color: colors.text }]}>✕</Text>
              </TouchableOpacity>
            </View>

            <View style={styles.inputContainer}>
              <Text style={[styles.inputLabel, { color: colors.text }]}>
                Password Baru
              </Text>
              <TextInput
                style={[styles.input, { borderColor: colors.tint, color: colors.text }]}
                value={passwordData.newPassword}
                onChangeText={(text) => setPasswordData({...passwordData, newPassword: text})}
                secureTextEntry
                placeholder="Minimal 6 karakter"
              />
            </View>

            <View style={styles.inputContainer}>
              <Text style={[styles.inputLabel, { color: colors.text }]}>
                Konfirmasi Password Baru
              </Text>
              <TextInput
                style={[styles.input, { borderColor: colors.tint, color: colors.text }]}
                value={passwordData.confirmPassword}
                onChangeText={(text) => setPasswordData({...passwordData, confirmPassword: text})}
                secureTextEntry
                placeholder="Ulangi password baru"
              />
            </View>

            <TouchableOpacity
              style={[styles.submitButton, { backgroundColor: colors.primary }]}
              onPress={handleChangePassword}
              disabled={passwordLoading}
            >
              {passwordLoading ? (
                <ActivityIndicator color="white" />
              ) : (
                <Text style={styles.submitButtonText}>Ubah Password</Text>
              )}
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  header: {
    padding: 20,
    paddingBottom: 10,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  profileCard: {
    margin: 20,
    padding: 24,
    borderRadius: 16,
    alignItems: 'center',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  avatarContainer: {
    marginBottom: 16,
  },
  avatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(255,255,255,0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  profileName: {
    color: 'white',
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  profileEmail: {
    color: 'white',
    fontSize: 14,
    opacity: 0.9,
  },
  section: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  infoCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 16,
    marginBottom: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 4,
    borderWidth: 1,
    borderColor: 'rgba(163, 183, 202, 0.15)',
  },
  infoContent: {
    marginLeft: 12,
    flex: 1,
  },
  infoLabel: {
    fontSize: 12,
    opacity: 0.7,
  },
  infoValue: {
    fontSize: 16,
    fontWeight: '600',
    marginTop: 2,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderRadius: 16,
    marginBottom: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 4,
    borderWidth: 1,
    borderColor: 'rgba(163, 183, 202, 0.15)',
  },
  menuLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  menuIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  menuTitle: {
    fontSize: 16,
    fontWeight: '500',
  },
  menuSubtitle: {
    fontSize: 12,
    opacity: 0.7,
    marginTop: 2,
  },
  logoutButton: {
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  logoutText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '90%',
    maxHeight: '80%',
    borderRadius: 16,
    padding: 20,
    elevation: 5,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  closeButton: {
    width: 30,
    height: 30,
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeButtonText: {
    fontSize: 18,
  },
  inputContainer: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
  },
  submitButton: {
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 8,
  },
  submitButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});
