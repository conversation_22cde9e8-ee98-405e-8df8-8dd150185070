import { Colors, Typography as TypographyTokens } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import React from 'react';
import { Text, TextProps } from 'react-native';

interface TypographyProps extends TextProps {
  variant?: 'h1' | 'h2' | 'h3' | 'h4' | 'body1' | 'body2' | 'caption' | 'overline';
  color?: 'primary' | 'secondary' | 'text' | 'textSecondary' | 'textMuted' | 'success' | 'warning' | 'error';
  weight?: 'light' | 'normal' | 'medium' | 'semibold' | 'bold' | 'extrabold';
  align?: 'left' | 'center' | 'right';
}

export function Typography({ 
  variant = 'body1', 
  color = 'text',
  weight,
  align = 'left',
  style,
  children,
  ...props 
}: TypographyProps) {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];
  
  const getVariantStyle = () => {
    switch (variant) {
      case 'h1':
        return {
          fontSize: TypographyTokens.fontSize['4xl'],
          fontWeight: TypographyTokens.fontWeight.bold,
          lineHeight: TypographyTokens.fontSize['4xl'] * TypographyTokens.lineHeight.tight,
        };
      case 'h2':
        return {
          fontSize: TypographyTokens.fontSize['3xl'],
          fontWeight: TypographyTokens.fontWeight.bold,
          lineHeight: TypographyTokens.fontSize['3xl'] * TypographyTokens.lineHeight.tight,
        };
      case 'h3':
        return {
          fontSize: TypographyTokens.fontSize['2xl'],
          fontWeight: TypographyTokens.fontWeight.semibold,
          lineHeight: TypographyTokens.fontSize['2xl'] * TypographyTokens.lineHeight.normal,
        };
      case 'h4':
        return {
          fontSize: TypographyTokens.fontSize.xl,
          fontWeight: TypographyTokens.fontWeight.semibold,
          lineHeight: TypographyTokens.fontSize.xl * TypographyTokens.lineHeight.normal,
        };
      case 'body1':
        return {
          fontSize: TypographyTokens.fontSize.base,
          fontWeight: TypographyTokens.fontWeight.normal,
          lineHeight: TypographyTokens.fontSize.base * TypographyTokens.lineHeight.normal,
        };
      case 'body2':
        return {
          fontSize: TypographyTokens.fontSize.sm,
          fontWeight: TypographyTokens.fontWeight.normal,
          lineHeight: TypographyTokens.fontSize.sm * TypographyTokens.lineHeight.normal,
        };
      case 'caption':
        return {
          fontSize: TypographyTokens.fontSize.xs,
          fontWeight: TypographyTokens.fontWeight.normal,
          lineHeight: TypographyTokens.fontSize.xs * TypographyTokens.lineHeight.normal,
        };
      case 'overline':
        return {
          fontSize: TypographyTokens.fontSize.xs,
          fontWeight: TypographyTokens.fontWeight.medium,
          lineHeight: TypographyTokens.fontSize.xs * TypographyTokens.lineHeight.normal,
          textTransform: 'uppercase' as const,
          letterSpacing: 0.5,
        };
      default:
        return {};
    }
  };
  
  const getColorStyle = () => {
    switch (color) {
      case 'primary':
        return { color: colors.primary };
      case 'secondary':
        return { color: colors.secondary };
      case 'text':
        return { color: colors.text };
      case 'textSecondary':
        return { color: colors.textSecondary };
      case 'textMuted':
        return { color: colors.textMuted };
      case 'success':
        return { color: colors.success };
      case 'warning':
        return { color: colors.warning };
      case 'error':
        return { color: colors.error };
      default:
        return { color: colors.text };
    }
  };
  
  const getWeightStyle = () => {
    if (!weight) return {};
    return { fontWeight: TypographyTokens.fontWeight[weight] };
  };
  
  const getAlignStyle = () => {
    return { textAlign: align };
  };
  
  return (
    <Text
      style={[
        getVariantStyle(),
        getColorStyle(),
        getWeightStyle(),
        getAlignStyle(),
        style,
      ]}
      {...props}
    >
      {children}
    </Text>
  );
}

// Convenience components for common use cases
export const Heading1 = (props: Omit<TypographyProps, 'variant'>) => (
  <Typography variant="h1" {...props} />
);

export const Heading2 = (props: Omit<TypographyProps, 'variant'>) => (
  <Typography variant="h2" {...props} />
);

export const Heading3 = (props: Omit<TypographyProps, 'variant'>) => (
  <Typography variant="h3" {...props} />
);

export const Heading4 = (props: Omit<TypographyProps, 'variant'>) => (
  <Typography variant="h4" {...props} />
);

export const Body1 = (props: Omit<TypographyProps, 'variant'>) => (
  <Typography variant="body1" {...props} />
);

export const Body2 = (props: Omit<TypographyProps, 'variant'>) => (
  <Typography variant="body2" {...props} />
);

export const Caption = (props: Omit<TypographyProps, 'variant'>) => (
  <Typography variant="caption" {...props} />
);

export const Overline = (props: Omit<TypographyProps, 'variant'>) => (
  <Typography variant="overline" {...props} />
);
