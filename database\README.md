# Tabungan Haji - Database Setup Guide

This guide will help you set up the Supabase database for your Tabungan Haji (Hajj Savings) application.

## 📋 Overview

The database schema includes:
- **Users**: User profiles and personal information
- **Targets**: Hajj savings targets and goals
- **Tabungan Data**: Current savings totals
- **Setoran History**: Deposit/transaction history
- **App Settings**: User preferences and settings

## 🚀 Quick Setup

### 1. Create Supabase Project

1. Go to [supabase.com](https://supabase.com)
2. Create a new project
3. Wait for the project to be ready
4. Note your project URL and anon key

### 2. Run the SQL Schema

1. Open your Supabase dashboard
2. Go to **SQL Editor**
3. Copy the entire content of `database/schema.sql`
4. Paste it into the SQL editor
5. Click **Run** to execute the schema

### 3. Configure Environment Variables

Update your `.env` or environment configuration:

```bash
EXPO_PUBLIC_SUPABASE_URL=your_project_url_here
EXPO_PUBLIC_SUPABASE_ANON_KEY=your_anon_key_here
```

### 4. Verify Setup

Run the verification script:

```bash
npx ts-node scripts/setup-database.ts
```

## 📊 Database Schema Details

### Tables Structure

#### `users`
- Extends Supabase auth.users
- Stores user profile information
- Automatically created when user signs up

#### `targets`
- One target per user (savings goal)
- Includes target amount, date, and Hajj package type
- Linked to user via foreign key

#### `tabungan_data`
- Current savings total for each user
- Automatically updated when deposits are made
- One record per user

#### `setoran_history`
- Complete history of all deposits
- Includes date, amount, and description
- Triggers automatic total calculation

#### `app_settings`
- User preferences (theme, notifications, language)
- One record per user
- Created automatically on user registration

### Key Features

#### 🔄 Automatic Calculations
- Total savings automatically calculated from deposit history
- Triggers ensure data consistency
- Real-time updates when deposits are added/modified

#### 🔒 Row Level Security (RLS)
- Users can only access their own data
- Secure by default
- Policies configured for all operations

#### ⚡ Performance Optimized
- Proper indexes on frequently queried columns
- Efficient queries for common operations
- Views for complex data aggregation

## 🛠️ Advanced Configuration

### Custom Functions

The schema includes several PostgreSQL functions:

- `calculate_total_tabungan(user_id)`: Calculate total savings
- `update_tabungan_total()`: Trigger function for automatic updates
- `handle_new_user()`: Initialize new user data

### Triggers

- **Updated At**: Automatically update timestamps
- **Total Calculation**: Update savings totals on deposit changes
- **New User**: Initialize user data on registration

### Views

- `user_summary`: Complete user info with progress calculations
- `setoran_summary`: Deposit history with running totals

## 🔍 Testing the Setup

### 1. Connection Test
```sql
SELECT COUNT(*) FROM users;
```

### 2. User Registration Test
- Register a new user through your app
- Check if user record is created automatically
- Verify tabungan_data and app_settings are initialized

### 3. Deposit Test
- Add a deposit through your app
- Verify setoran_history record is created
- Check that tabungan_data.total_tabungan is updated automatically

## 📈 Monitoring and Maintenance

### Useful Queries

#### Check User Statistics
```sql
SELECT 
    COUNT(*) as total_users,
    COUNT(t.id) as users_with_targets,
    AVG(td.total_tabungan) as avg_savings,
    SUM(td.total_tabungan) as total_savings
FROM users u
LEFT JOIN targets t ON u.id = t.user_id
LEFT JOIN tabungan_data td ON u.id = td.user_id;
```

#### Find Users Close to Target
```sql
SELECT 
    u.nama,
    u.email,
    t.target_biaya,
    td.total_tabungan,
    ROUND((td.total_tabungan::DECIMAL / t.target_biaya::DECIMAL) * 100, 2) as progress
FROM users u
JOIN targets t ON u.id = t.user_id
JOIN tabungan_data td ON u.id = td.user_id
WHERE td.total_tabungan::DECIMAL / t.target_biaya::DECIMAL >= 0.9
ORDER BY progress DESC;
```

## 🚨 Troubleshooting

### Common Issues

#### 1. RLS Policy Errors
- Ensure user is authenticated
- Check if policies allow the operation
- Verify user_id matches auth.uid()

#### 2. Trigger Not Working
- Check if functions exist
- Verify trigger is enabled
- Look for error messages in logs

#### 3. Connection Issues
- Verify Supabase URL and key
- Check network connectivity
- Ensure project is active

### Debug Steps

1. **Check Supabase Logs**: Go to Logs section in dashboard
2. **Test Queries**: Use SQL Editor to test queries manually
3. **Verify RLS**: Temporarily disable RLS to test queries
4. **Check Auth**: Ensure user is properly authenticated

## 🔄 Migration from Local Storage

If you're migrating from local storage:

1. The app includes migration utilities in `utils/migration.ts`
2. Migration runs automatically on first app launch
3. Data is safely transferred to Supabase
4. Local data is preserved as backup

## 📞 Support

If you encounter issues:

1. Check the troubleshooting section above
2. Review Supabase documentation
3. Check the app's error handling logs
4. Verify your schema matches the provided SQL

## 🎯 Next Steps

After successful setup:

1. Test user registration flow
2. Test deposit functionality
3. Verify data synchronization
4. Test offline/online scenarios
5. Deploy to production when ready

---

**Note**: This schema is designed for the Tabungan Haji app's specific requirements. Modify as needed for your use case.
