import AsyncStorage from '@react-native-async-storage/async-storage';

// Storage keys
export const STORAGE_KEYS = {
  USER_DATA: 'user_data',
  TABUNGAN_DATA: 'tabungan_data',
  TARGET_DATA: 'target_data',
  SETORAN_HISTORY: 'setoran_history',
  APP_SETTINGS: 'app_settings',
} as const;

// Generic storage functions
export const storage = {
  // Save data to storage
  async setItem<T>(key: string, value: T): Promise<void> {
    try {
      const jsonValue = JSON.stringify(value);
      await AsyncStorage.setItem(key, jsonValue);
    } catch (error) {
      console.error('Error saving to storage:', error);
      throw error;
    }
  },

  // Get data from storage
  async getItem<T>(key: string): Promise<T | null> {
    try {
      const jsonValue = await AsyncStorage.getItem(key);
      return jsonValue != null ? JSON.parse(jsonValue) : null;
    } catch (error) {
      console.error('Error reading from storage:', error);
      return null;
    }
  },

  // Remove item from storage
  async removeItem(key: string): Promise<void> {
    try {
      await AsyncStorage.removeItem(key);
    } catch (error) {
      console.error('Error removing from storage:', error);
      throw error;
    }
  },

  // Clear all storage
  async clear(): Promise<void> {
    try {
      await AsyncStorage.clear();
    } catch (error) {
      console.error('Error clearing storage:', error);
      throw error;
    }
  },

  // Get multiple items
  async multiGet(keys: string[]): Promise<Record<string, any>> {
    try {
      const result = await AsyncStorage.multiGet(keys);
      const data: Record<string, any> = {};
      
      result.forEach(([key, value]) => {
        if (value) {
          try {
            data[key] = JSON.parse(value);
          } catch {
            data[key] = value;
          }
        }
      });
      
      return data;
    } catch (error) {
      console.error('Error getting multiple items:', error);
      return {};
    }
  },
};

// Specific data types
export interface UserData {
  nama: string;
  email: string;
  telepon: string;
  alamat: string;
  tanggalLahir: string;
}

export interface TargetData {
  targetBiaya: number;
  tanggalTarget: string;
  paketHaji: 'reguler' | 'plus' | 'khusus';
}

export interface SetoranItem {
  id: number;
  tanggal: string;
  jumlah: number;
  keterangan: string;
}

export interface TabunganData {
  totalTabungan: number;
  riwayatSetoran: SetoranItem[];
  lastUpdated: string;
}

export interface AppSettings {
  theme: 'light' | 'dark' | 'system';
  notifications: boolean;
  language: 'id' | 'en';
}

// Specific storage functions
export const userStorage = {
  async saveUserData(userData: UserData): Promise<void> {
    return storage.setItem(STORAGE_KEYS.USER_DATA, userData);
  },

  async getUserData(): Promise<UserData | null> {
    return storage.getItem<UserData>(STORAGE_KEYS.USER_DATA);
  },
};

export const tabunganStorage = {
  async saveTabunganData(tabunganData: TabunganData): Promise<void> {
    return storage.setItem(STORAGE_KEYS.TABUNGAN_DATA, {
      ...tabunganData,
      lastUpdated: new Date().toISOString(),
    });
  },

  async getTabunganData(): Promise<TabunganData | null> {
    return storage.getItem<TabunganData>(STORAGE_KEYS.TABUNGAN_DATA);
  },

  async addSetoran(setoran: Omit<SetoranItem, 'id'>): Promise<void> {
    const currentData = await this.getTabunganData();
    const riwayatSetoran = currentData?.riwayatSetoran || [];
    
    const newSetoran: SetoranItem = {
      ...setoran,
      id: Date.now(), // Simple ID generation
    };

    const updatedRiwayat = [newSetoran, ...riwayatSetoran];
    const totalTabungan = updatedRiwayat.reduce((sum, item) => sum + item.jumlah, 0);

    await this.saveTabunganData({
      totalTabungan,
      riwayatSetoran: updatedRiwayat,
      lastUpdated: new Date().toISOString(),
    });
  },
};

export const targetStorage = {
  async saveTargetData(targetData: TargetData): Promise<void> {
    return storage.setItem(STORAGE_KEYS.TARGET_DATA, targetData);
  },

  async getTargetData(): Promise<TargetData | null> {
    return storage.getItem<TargetData>(STORAGE_KEYS.TARGET_DATA);
  },
};

export const settingsStorage = {
  async saveSettings(settings: AppSettings): Promise<void> {
    return storage.setItem(STORAGE_KEYS.APP_SETTINGS, settings);
  },

  async getSettings(): Promise<AppSettings | null> {
    return storage.getItem<AppSettings>(STORAGE_KEYS.APP_SETTINGS);
  },
};
