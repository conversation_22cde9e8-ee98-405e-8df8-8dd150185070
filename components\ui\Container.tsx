import React from 'react';
import { View, ViewProps, StyleSheet } from 'react-native';
import { Spacing } from '@/constants/Colors';

interface ContainerProps extends ViewProps {
  padding?: 'none' | 'xs' | 'sm' | 'md' | 'base' | 'lg' | 'xl' | '2xl';
  paddingHorizontal?: 'none' | 'xs' | 'sm' | 'md' | 'base' | 'lg' | 'xl' | '2xl';
  paddingVertical?: 'none' | 'xs' | 'sm' | 'md' | 'base' | 'lg' | 'xl' | '2xl';
  margin?: 'none' | 'xs' | 'sm' | 'md' | 'base' | 'lg' | 'xl' | '2xl';
  marginHorizontal?: 'none' | 'xs' | 'sm' | 'md' | 'base' | 'lg' | 'xl' | '2xl';
  marginVertical?: 'none' | 'xs' | 'sm' | 'md' | 'base' | 'lg' | 'xl' | '2xl';
  gap?: 'none' | 'xs' | 'sm' | 'md' | 'base' | 'lg' | 'xl' | '2xl';
  align?: 'flex-start' | 'center' | 'flex-end' | 'stretch';
  justify?: 'flex-start' | 'center' | 'flex-end' | 'space-between' | 'space-around' | 'space-evenly';
  direction?: 'row' | 'column';
  flex?: number;
}

export function Container({ 
  padding,
  paddingHorizontal,
  paddingVertical,
  margin,
  marginHorizontal,
  marginVertical,
  gap,
  align,
  justify,
  direction = 'column',
  flex,
  style,
  children,
  ...props 
}: ContainerProps) {
  
  const getSpacingValue = (size?: string) => {
    if (!size || size === 'none') return 0;
    
    switch (size) {
      case 'xs':
        return Spacing.xs;
      case 'sm':
        return Spacing.sm;
      case 'md':
        return Spacing.md;
      case 'base':
        return Spacing.base;
      case 'lg':
        return Spacing.lg;
      case 'xl':
        return Spacing.xl;
      case '2xl':
        return Spacing['2xl'];
      default:
        return 0;
    }
  };
  
  const containerStyle = {
    flexDirection: direction,
    alignItems: align,
    justifyContent: justify,
    flex: flex,
    padding: getSpacingValue(padding),
    paddingHorizontal: paddingHorizontal ? getSpacingValue(paddingHorizontal) : undefined,
    paddingVertical: paddingVertical ? getSpacingValue(paddingVertical) : undefined,
    margin: getSpacingValue(margin),
    marginHorizontal: marginHorizontal ? getSpacingValue(marginHorizontal) : undefined,
    marginVertical: marginVertical ? getSpacingValue(marginVertical) : undefined,
    gap: getSpacingValue(gap),
  };
  
  return (
    <View 
      style={[containerStyle, style]} 
      {...props}
    >
      {children}
    </View>
  );
}

// Convenience components for common layouts
export const Row = (props: Omit<ContainerProps, 'direction'>) => (
  <Container direction="row" {...props} />
);

export const Column = (props: Omit<ContainerProps, 'direction'>) => (
  <Container direction="column" {...props} />
);

export const Center = (props: ContainerProps) => (
  <Container align="center" justify="center" {...props} />
);

export const SpaceBetween = (props: Omit<ContainerProps, 'justify'>) => (
  <Container justify="space-between" {...props} />
);

export const FlexRow = (props: Omit<ContainerProps, 'direction' | 'flex'>) => (
  <Container direction="row" flex={1} {...props} />
);

export const FlexColumn = (props: Omit<ContainerProps, 'direction' | 'flex'>) => (
  <Container direction="column" flex={1} {...props} />
);
