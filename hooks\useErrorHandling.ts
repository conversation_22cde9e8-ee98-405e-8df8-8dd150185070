import { useState, useEffect, useCallback } from 'react';
import { 
  AppError, 
  ErrorType, 
  handleSupabaseError, 
  withRetry, 
  withLoading,
  globalLoadingManager,
  globalOfflineQueue,
  isOnline 
} from '@/utils/errorHandling';

interface ErrorState {
  error: AppError | null;
  isLoading: boolean;
  retryCount: number;
}

interface UseErrorHandlingOptions {
  maxRetries?: number;
  retryDelay?: number;
  enableOfflineQueue?: boolean;
  loadingKey?: string;
}

export function useErrorHandling(options: UseErrorHandlingOptions = {}) {
  const {
    maxRetries = 3,
    retryDelay = 1000,
    enableOfflineQueue = true,
    loadingKey = 'default',
  } = options;

  const [state, setState] = useState<ErrorState>({
    error: null,
    isLoading: false,
    retryCount: 0,
  });

  const [isOnlineState, setIsOnlineState] = useState(isOnline());

  // Listen to online/offline events
  useEffect(() => {
    const handleOnline = () => {
      setIsOnlineState(true);
      if (enableOfflineQueue) {
        globalOfflineQueue.processQueue();
      }
    };

    const handleOffline = () => {
      setIsOnlineState(false);
    };

    if (typeof window !== 'undefined') {
      window.addEventListener('online', handleOnline);
      window.addEventListener('offline', handleOffline);

      return () => {
        window.removeEventListener('online', handleOnline);
        window.removeEventListener('offline', handleOffline);
      };
    }
  }, [enableOfflineQueue]);

  // Listen to global loading state changes
  useEffect(() => {
    const unsubscribe = globalLoadingManager.subscribe((loadingStates) => {
      const isCurrentlyLoading = loadingStates[loadingKey] || false;
      setState(prev => ({ ...prev, isLoading: isCurrentlyLoading }));
    });

    return unsubscribe;
  }, [loadingKey]);

  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null, retryCount: 0 }));
  }, []);

  const setError = useCallback((error: AppError | Error | string) => {
    let appError: AppError;
    
    if (error instanceof AppError) {
      appError = error;
    } else if (error instanceof Error) {
      appError = handleSupabaseError(error, 'operation');
    } else {
      appError = new AppError(error, ErrorType.UNKNOWN);
    }

    setState(prev => ({ ...prev, error: appError }));
  }, []);

  const executeWithErrorHandling = useCallback(async <T>(
    operation: () => Promise<T>,
    operationName: string = 'operation',
    context?: Record<string, any>
  ): Promise<T | null> => {
    try {
      clearError();
      
      const result = await withLoading(
        loadingKey,
        () => withRetry(operation, maxRetries, retryDelay)
      );
      
      setState(prev => ({ ...prev, retryCount: 0 }));
      return result;
    } catch (error) {
      const appError = handleSupabaseError(error, operationName, context);
      
      // If offline and queue is enabled, add to queue
      if (!isOnlineState && enableOfflineQueue && appError.type === ErrorType.NETWORK) {
        globalOfflineQueue.addToQueue(`${operationName}-${Date.now()}`, operation);
        setError(new AppError(
          'Operasi disimpan dan akan dijalankan saat koneksi tersedia',
          ErrorType.NETWORK
        ));
      } else {
        setState(prev => ({ 
          ...prev, 
          error: appError, 
          retryCount: prev.retryCount + 1 
        }));
      }
      
      return null;
    }
  }, [clearError, loadingKey, maxRetries, retryDelay, isOnlineState, enableOfflineQueue]);

  const retry = useCallback(async <T>(
    operation: () => Promise<T>,
    operationName: string = 'operation'
  ): Promise<T | null> => {
    if (!state.error) return null;
    
    return executeWithErrorHandling(operation, operationName);
  }, [state.error, executeWithErrorHandling]);

  return {
    // State
    error: state.error,
    isLoading: state.isLoading,
    retryCount: state.retryCount,
    isOnline: isOnlineState,
    
    // Actions
    clearError,
    setError,
    executeWithErrorHandling,
    retry,
    
    // Computed values
    hasError: state.error !== null,
    canRetry: state.error?.type === ErrorType.NETWORK || state.error?.type === ErrorType.DATABASE,
    errorMessage: state.error?.message || null,
    errorType: state.error?.type || null,
    
    // Queue info
    queueSize: enableOfflineQueue ? globalOfflineQueue.getQueueSize() : 0,
  };
}

// Specialized hook for database operations
export function useDatabaseOperation(operationName: string) {
  const errorHandling = useErrorHandling({
    maxRetries: 3,
    retryDelay: 1000,
    enableOfflineQueue: true,
    loadingKey: `db-${operationName}`,
  });

  const execute = useCallback(async <T>(
    operation: () => Promise<T>,
    context?: Record<string, any>
  ): Promise<T | null> => {
    return errorHandling.executeWithErrorHandling(operation, operationName, context);
  }, [errorHandling, operationName]);

  return {
    ...errorHandling,
    execute,
  };
}

// Hook for handling multiple concurrent operations
export function useMultipleOperations() {
  const [operations, setOperations] = useState<Map<string, ErrorState>>(new Map());

  const executeOperation = useCallback(async <T>(
    key: string,
    operation: () => Promise<T>,
    options: UseErrorHandlingOptions = {}
  ): Promise<T | null> => {
    const { maxRetries = 3, retryDelay = 1000 } = options;

    // Set loading state
    setOperations(prev => new Map(prev.set(key, {
      error: null,
      isLoading: true,
      retryCount: prev.get(key)?.retryCount || 0,
    })));

    try {
      const result = await withLoading(
        `multi-${key}`,
        () => withRetry(operation, maxRetries, retryDelay)
      );

      // Set success state
      setOperations(prev => new Map(prev.set(key, {
        error: null,
        isLoading: false,
        retryCount: 0,
      })));

      return result;
    } catch (error) {
      const appError = handleSupabaseError(error, key);
      
      // Set error state
      setOperations(prev => {
        const current = prev.get(key) || { error: null, isLoading: false, retryCount: 0 };
        return new Map(prev.set(key, {
          error: appError,
          isLoading: false,
          retryCount: current.retryCount + 1,
        }));
      });

      return null;
    }
  }, []);

  const clearOperation = useCallback((key: string) => {
    setOperations(prev => {
      const newMap = new Map(prev);
      newMap.delete(key);
      return newMap;
    });
  }, []);

  const getOperationState = useCallback((key: string): ErrorState => {
    return operations.get(key) || { error: null, isLoading: false, retryCount: 0 };
  }, [operations]);

  return {
    executeOperation,
    clearOperation,
    getOperationState,
    isAnyLoading: Array.from(operations.values()).some(op => op.isLoading),
    hasAnyError: Array.from(operations.values()).some(op => op.error !== null),
    operations: Object.fromEntries(operations),
  };
}
