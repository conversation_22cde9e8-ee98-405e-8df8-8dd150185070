import React, { useState } from 'react';
import {
    Alert,
    ScrollView,
    StyleSheet,
    Text,
    TextInput,
    TouchableOpacity,
    View,
} from 'react-native';
import { authDebug } from '../../utils/authDebug';

export default function AuthDebugScreen() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [results, setResults] = useState<string[]>([]);

  const addResult = (message: string) => {
    setResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const clearResults = () => {
    setResults([]);
  };

  const testConnection = async () => {
    addResult('Testing connection...');
    const result = await authDebug.testConnection();
    addResult(`Connection: ${result ? 'SUCCESS' : 'FAILED'}`);
  };

  const checkConfig = async () => {
    addResult('Checking Supabase configuration...');
    await authDebug.checkSupabaseConfig();
    addResult('Config check complete - check console for details');
  };

  const runDiagnostics = async () => {
    addResult('Running diagnostics...');
    await authDebug.runDiagnostics();
    addResult('Diagnostics complete - check console for details');
  };

  const testRegistration = async () => {
    if (!email || !password) {
      Alert.alert('Error', 'Please enter email and password');
      return;
    }
    
    addResult(`Testing registration for: ${email}`);
    const result = await authDebug.testRegistration(email, password);
    addResult(`Registration: ${result.success ? 'SUCCESS' : 'FAILED'}`);
    if (!result.success) {
      addResult(`Error: ${result.error}`);
    }
  };

  const testLogin = async () => {
    if (!email || !password) {
      Alert.alert('Error', 'Please enter email and password');
      return;
    }
    
    addResult(`Testing login for: ${email}`);
    const result = await authDebug.testLogin(email, password);
    addResult(`Login: ${result.success ? 'SUCCESS' : 'FAILED'}`);
    if (!result.success) {
      addResult(`Error: ${result.error}`);
    }
  };

  const checkUserExists = async () => {
    if (!email) {
      Alert.alert('Error', 'Please enter email');
      return;
    }

    addResult(`Checking if user exists: ${email}`);
    const result = await authDebug.checkUserExists(email);
    addResult(`User exists: ${result.exists ? 'YES' : 'NO'}`);
    addResult(`Message: ${result.message}`);
  };

  const testNavigation = () => {
    addResult('Testing navigation to tabs...');
    try {
      // This will test if the navigation works
      // Note: This might fail if user is not authenticated
      addResult('Attempting to navigate to /(tabs)');
      // Uncomment the next line to test navigation
      // router.push('/(tabs)');
      addResult('Navigation test - check manually');
    } catch (err) {
      addResult(`Navigation failed: ${err}`);
    }
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Auth Debug Tool</Text>
        <Text style={styles.subtitle}>Test authentication functionality</Text>
      </View>

      <View style={styles.form}>
        <Text style={styles.label}>Test Email:</Text>
        <TextInput
          style={styles.input}
          value={email}
          onChangeText={setEmail}
          placeholder="Enter email to test"
          keyboardType="email-address"
          autoCapitalize="none"
        />

        <Text style={styles.label}>Test Password:</Text>
        <TextInput
          style={styles.input}
          value={password}
          onChangeText={setPassword}
          placeholder="Enter password to test"
          secureTextEntry
        />

        <View style={styles.buttonContainer}>
          <TouchableOpacity style={styles.button} onPress={testConnection}>
            <Text style={styles.buttonText}>Test Connection</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.button} onPress={checkConfig}>
            <Text style={styles.buttonText}>Check Config</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.button} onPress={runDiagnostics}>
            <Text style={styles.buttonText}>Run Diagnostics</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.button} onPress={checkUserExists}>
            <Text style={styles.buttonText}>Check User Exists</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.button} onPress={testRegistration}>
            <Text style={styles.buttonText}>Test Registration</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.button} onPress={testLogin}>
            <Text style={styles.buttonText}>Test Login</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.button} onPress={testNavigation}>
            <Text style={styles.buttonText}>Test Navigation</Text>
          </TouchableOpacity>

          <TouchableOpacity style={[styles.button, styles.clearButton]} onPress={clearResults}>
            <Text style={styles.buttonText}>Clear Results</Text>
          </TouchableOpacity>
        </View>
      </View>

      <View style={styles.results}>
        <Text style={styles.resultsTitle}>Test Results:</Text>
        {results.map((result, index) => (
          <Text key={index} style={styles.resultText}>
            {result}
          </Text>
        ))}
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
    padding: 20,
  },
  header: {
    alignItems: 'center',
    marginBottom: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#194a7a',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#7593af',
    textAlign: 'center',
  },
  form: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 20,
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: '#194a7a',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: '#d1dbe4',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: '#f8f9fa',
    marginBottom: 16,
  },
  buttonContainer: {
    gap: 10,
  },
  button: {
    backgroundColor: '#194a7a',
    borderRadius: 8,
    padding: 12,
    alignItems: 'center',
  },
  clearButton: {
    backgroundColor: '#476f95',
  },
  buttonText: {
    color: '#ffffff',
    fontSize: 14,
    fontWeight: '600',
  },
  results: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 20,
  },
  resultsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#194a7a',
    marginBottom: 10,
  },
  resultText: {
    fontSize: 12,
    color: '#333',
    marginBottom: 4,
    fontFamily: 'monospace',
  },
});
