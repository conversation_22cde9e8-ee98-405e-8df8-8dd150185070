/**
 * Database Setup Script for Tabungan Haji Supabase Migration
 * 
 * This script helps verify the database setup and can be used to test
 * the Supabase connection and basic operations.
 */

import { supabase, userService, combinedService } from '../utils/supabase';

// Test data for verification
const testUser = {
  nama: 'Test User Setup',
  email: '<EMAIL>',
  telepon: '081234567890',
  alamat: 'Test Address for Setup',
  tanggal_lahir: '1990-01-01',
};

const testTarget = {
  target_biaya: 35000000,
  tanggal_target: '2025-12-31',
  paket_haji: 'reguler' as const,
};

/**
 * Test database connection
 */
async function testConnection(): Promise<boolean> {
  try {
    console.log('🔗 Testing Supabase connection...');
    
    const { data, error } = await supabase
      .from('users')
      .select('count')
      .limit(1);
    
    if (error) {
      console.error('❌ Connection failed:', error.message);
      return false;
    }
    
    console.log('✅ Connection successful!');
    return true;
  } catch (error) {
    console.error('❌ Connection error:', error);
    return false;
  }
}

/**
 * Verify all tables exist
 */
async function verifyTables(): Promise<boolean> {
  const tables = ['users', 'targets', 'tabungan_data', 'setoran_history', 'app_settings'];
  
  console.log('📋 Verifying database tables...');
  
  for (const table of tables) {
    try {
      const { error } = await supabase
        .from(table)
        .select('*')
        .limit(1);
      
      if (error) {
        console.error(`❌ Table '${table}' not found or accessible:`, error.message);
        return false;
      }
      
      console.log(`✅ Table '${table}' exists and accessible`);
    } catch (error) {
      console.error(`❌ Error checking table '${table}':`, error);
      return false;
    }
  }
  
  return true;
}

/**
 * Test basic CRUD operations
 */
async function testCRUDOperations(): Promise<boolean> {
  try {
    console.log('🧪 Testing CRUD operations...');
    
    // Test user creation
    console.log('  Creating test user...');
    const result = await combinedService.initializeUser(testUser, testTarget);
    const userId = result.user.id;
    console.log(`  ✅ User created with ID: ${userId}`);
    
    // Test user retrieval
    console.log('  Retrieving user...');
    const retrievedUser = await userService.getUserById(userId);
    if (!retrievedUser) {
      throw new Error('Failed to retrieve created user');
    }
    console.log('  ✅ User retrieved successfully');
    
    // Test user update
    console.log('  Updating user...');
    await userService.updateUser(userId, { nama: 'Updated Test User' });
    console.log('  ✅ User updated successfully');
    
    // Cleanup - delete test user
    console.log('  Cleaning up test data...');
    await userService.deleteUser(userId);
    console.log('  ✅ Test data cleaned up');
    
    return true;
  } catch (error) {
    console.error('❌ CRUD operations failed:', error);
    return false;
  }
}

/**
 * Test database triggers
 */
async function testTriggers(): Promise<boolean> {
  try {
    console.log('⚡ Testing database triggers...');
    
    // Create test user
    const result = await combinedService.initializeUser(testUser, testTarget);
    const userId = result.user.id;
    
    // Add some setoran to test automatic total calculation
    const { setoranService, tabunganService } = await import('../utils/supabase');
    
    await setoranService.addSetoran({
      user_id: userId,
      tanggal: '2024-01-15',
      jumlah: 500000,
      keterangan: 'Test setoran 1',
    });
    
    await setoranService.addSetoran({
      user_id: userId,
      tanggal: '2024-02-15',
      jumlah: 750000,
      keterangan: 'Test setoran 2',
    });
    
    // Check if total was calculated automatically
    const tabunganData = await tabunganService.getTabunganDataByUserId(userId);
    
    if (!tabunganData || tabunganData.total_tabungan !== 1250000) {
      throw new Error(`Expected total 1250000, got ${tabunganData?.total_tabungan}`);
    }
    
    console.log('  ✅ Automatic total calculation working');
    
    // Cleanup
    await userService.deleteUser(userId);
    console.log('  ✅ Trigger test completed');
    
    return true;
  } catch (error) {
    console.error('❌ Trigger test failed:', error);
    return false;
  }
}

/**
 * Main setup verification function
 */
export async function verifyDatabaseSetup(): Promise<void> {
  console.log('🚀 Starting Supabase Database Setup Verification\n');
  
  const tests = [
    { name: 'Connection Test', fn: testConnection },
    { name: 'Table Verification', fn: verifyTables },
    { name: 'CRUD Operations', fn: testCRUDOperations },
    { name: 'Database Triggers', fn: testTriggers },
  ];
  
  let allPassed = true;
  
  for (const test of tests) {
    console.log(`\n📝 Running ${test.name}...`);
    const passed = await test.fn();
    
    if (!passed) {
      allPassed = false;
      console.log(`❌ ${test.name} failed\n`);
    } else {
      console.log(`✅ ${test.name} passed\n`);
    }
  }
  
  console.log('=' .repeat(50));
  
  if (allPassed) {
    console.log('🎉 All tests passed! Your Supabase database is ready.');
    console.log('\nNext steps:');
    console.log('1. Run your app and test the migration');
    console.log('2. Check that existing data migrates correctly');
    console.log('3. Test all app functionality');
    console.log('4. Deploy to production when ready');
  } else {
    console.log('❌ Some tests failed. Please check the errors above.');
    console.log('\nTroubleshooting:');
    console.log('1. Verify you ran the SQL schema in Supabase');
    console.log('2. Check your Supabase URL and API key');
    console.log('3. Ensure RLS policies allow the operations');
    console.log('4. Check the database/README.md for setup instructions');
  }
  
  console.log('\n📚 Documentation:');
  console.log('- Database schema: database/schema.sql');
  console.log('- Setup guide: database/README.md');
  console.log('- Migration notes: MIGRATION_NOTES.md');
  console.log('- Complete guide: SUPABASE_MIGRATION_COMPLETE.md');
}

// Run verification if this script is executed directly
if (require.main === module) {
  verifyDatabaseSetup().catch(console.error);
}
