import { PostgrestError } from '@supabase/supabase-js';

// Error types for better categorization
export enum ErrorType {
  NETWORK = 'NETWORK',
  DATABASE = 'DATABASE',
  VALIDATION = 'VALIDATION',
  AUTHENTICATION = 'AUTHENTICATION',
  PERMISSION = 'PERMISSION',
  NOT_FOUND = 'NOT_FOUND',
  CONFLICT = 'CONFLICT',
  UNKNOWN = 'UNKNOWN',
}

// Custom error class for app-specific errors
export class AppError extends Error {
  public readonly type: ErrorType;
  public readonly code?: string;
  public readonly details?: any;
  public readonly timestamp: Date;

  constructor(
    message: string,
    type: ErrorType = ErrorType.UNKNOWN,
    code?: string,
    details?: any
  ) {
    super(message);
    this.name = 'AppError';
    this.type = type;
    this.code = code;
    this.details = details;
    this.timestamp = new Date();
  }
}

// Network connectivity check
export const isOnline = (): boolean => {
  if (typeof navigator !== 'undefined' && 'onLine' in navigator) {
    return navigator.onLine;
  }
  return true; // Assume online if can't detect
};

// Categorize Supabase errors
export const categorizeSupabaseError = (error: PostgrestError | Error): ErrorType => {
  if ('code' in error && error.code) {
    // PostgreSQL error codes
    switch (error.code) {
      case 'PGRST116': // No rows returned
        return ErrorType.NOT_FOUND;
      case '23505': // Unique violation
        return ErrorType.CONFLICT;
      case '23503': // Foreign key violation
      case '23502': // Not null violation
      case '23514': // Check violation
        return ErrorType.VALIDATION;
      case '42501': // Insufficient privilege
        return ErrorType.PERMISSION;
      case '08000': // Connection exception
      case '08003': // Connection does not exist
      case '08006': // Connection failure
        return ErrorType.NETWORK;
      default:
        return ErrorType.DATABASE;
    }
  }

  // Check error message for common patterns
  const message = error.message.toLowerCase();
  if (message.includes('network') || message.includes('connection') || message.includes('timeout')) {
    return ErrorType.NETWORK;
  }
  if (message.includes('unauthorized') || message.includes('forbidden')) {
    return ErrorType.AUTHENTICATION;
  }
  if (message.includes('not found')) {
    return ErrorType.NOT_FOUND;
  }
  if (message.includes('validation') || message.includes('invalid')) {
    return ErrorType.VALIDATION;
  }

  return ErrorType.UNKNOWN;
};

// Enhanced error handler for Supabase operations
export const handleSupabaseError = (
  error: any,
  operation: string,
  context?: Record<string, any>
): AppError => {
  console.error(`Supabase ${operation} error:`, error, context);

  const errorType = categorizeSupabaseError(error);
  let userMessage: string;

  // Generate user-friendly messages based on error type
  switch (errorType) {
    case ErrorType.NETWORK:
      userMessage = isOnline() 
        ? 'Koneksi ke server bermasalah. Silakan coba lagi.'
        : 'Tidak ada koneksi internet. Periksa koneksi Anda.';
      break;
    case ErrorType.NOT_FOUND:
      userMessage = 'Data tidak ditemukan.';
      break;
    case ErrorType.CONFLICT:
      userMessage = 'Data sudah ada atau terjadi konflik.';
      break;
    case ErrorType.VALIDATION:
      userMessage = 'Data tidak valid. Periksa input Anda.';
      break;
    case ErrorType.AUTHENTICATION:
      userMessage = 'Sesi Anda telah berakhir. Silakan login kembali.';
      break;
    case ErrorType.PERMISSION:
      userMessage = 'Anda tidak memiliki izin untuk melakukan operasi ini.';
      break;
    case ErrorType.DATABASE:
      userMessage = 'Terjadi kesalahan pada database. Silakan coba lagi.';
      break;
    default:
      userMessage = `Gagal melakukan ${operation}. Silakan coba lagi.`;
  }

  return new AppError(
    userMessage,
    errorType,
    error.code,
    {
      operation,
      originalError: error.message,
      context,
    }
  );
};

// Retry mechanism for failed operations
export const withRetry = async <T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000,
  backoffMultiplier: number = 2
): Promise<T> => {
  let lastError: Error;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error as Error;
      
      // Don't retry for certain error types
      if (error instanceof AppError) {
        if ([ErrorType.VALIDATION, ErrorType.AUTHENTICATION, ErrorType.PERMISSION].includes(error.type)) {
          throw error;
        }
      }

      if (attempt === maxRetries) {
        break;
      }

      // Wait before retrying with exponential backoff
      const waitTime = delay * Math.pow(backoffMultiplier, attempt - 1);
      await new Promise(resolve => setTimeout(resolve, waitTime));
      
      console.log(`Retrying operation (attempt ${attempt + 1}/${maxRetries}) after ${waitTime}ms`);
    }
  }

  throw lastError!;
};

// Loading state manager
export class LoadingManager {
  private loadingStates: Map<string, boolean> = new Map();
  private listeners: Set<(states: Record<string, boolean>) => void> = new Set();

  setLoading(key: string, loading: boolean): void {
    this.loadingStates.set(key, loading);
    this.notifyListeners();
  }

  isLoading(key: string): boolean {
    return this.loadingStates.get(key) || false;
  }

  isAnyLoading(): boolean {
    return Array.from(this.loadingStates.values()).some(loading => loading);
  }

  getLoadingStates(): Record<string, boolean> {
    return Object.fromEntries(this.loadingStates);
  }

  subscribe(listener: (states: Record<string, boolean>) => void): () => void {
    this.listeners.add(listener);
    return () => this.listeners.delete(listener);
  }

  private notifyListeners(): void {
    const states = this.getLoadingStates();
    this.listeners.forEach(listener => listener(states));
  }

  clear(): void {
    this.loadingStates.clear();
    this.notifyListeners();
  }
}

// Global loading manager instance
export const globalLoadingManager = new LoadingManager();

// Wrapper for operations with loading state management
export const withLoading = async <T>(
  key: string,
  operation: () => Promise<T>,
  loadingManager: LoadingManager = globalLoadingManager
): Promise<T> => {
  try {
    loadingManager.setLoading(key, true);
    return await operation();
  } finally {
    loadingManager.setLoading(key, false);
  }
};

// Offline capability helpers
export const createOfflineQueue = () => {
  const queue: Array<{
    id: string;
    operation: () => Promise<any>;
    timestamp: Date;
    retries: number;
  }> = [];

  const addToQueue = (id: string, operation: () => Promise<any>) => {
    queue.push({
      id,
      operation,
      timestamp: new Date(),
      retries: 0,
    });
  };

  const processQueue = async () => {
    if (!isOnline()) {
      console.log('Still offline, skipping queue processing');
      return;
    }

    const failedOperations: typeof queue = [];

    for (const item of queue) {
      try {
        await item.operation();
        console.log(`Successfully processed queued operation: ${item.id}`);
      } catch (error) {
        item.retries++;
        if (item.retries < 3) {
          failedOperations.push(item);
        } else {
          console.error(`Failed to process queued operation after 3 retries: ${item.id}`, error);
        }
      }
    }

    // Replace queue with failed operations for retry
    queue.length = 0;
    queue.push(...failedOperations);
  };

  return {
    addToQueue,
    processQueue,
    getQueueSize: () => queue.length,
    clearQueue: () => queue.length = 0,
  };
};

// Global offline queue
export const globalOfflineQueue = createOfflineQueue();

// Error boundary helper for React components
export const createErrorBoundary = (fallbackComponent: React.ComponentType<{ error: Error }>) => {
  return class ErrorBoundary extends React.Component<
    { children: React.ReactNode },
    { hasError: boolean; error?: Error }
  > {
    constructor(props: { children: React.ReactNode }) {
      super(props);
      this.state = { hasError: false };
    }

    static getDerivedStateFromError(error: Error) {
      return { hasError: true, error };
    }

    componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
      console.error('Error boundary caught an error:', error, errorInfo);
    }

    render() {
      if (this.state.hasError && this.state.error) {
        return React.createElement(fallbackComponent, { error: this.state.error });
      }

      return this.props.children;
    }
  };
};
