# Migration Guide: Local Storage to Supabase

This guide explains how to migrate your Tabungan Haji app from local storage to Supabase database.

## 📋 Overview

The migration process:
1. **Automatic**: Runs on first app launch after Supabase setup
2. **Safe**: Preserves existing local data as backup
3. **Seamless**: Users won't lose any data
4. **One-time**: Migration runs only once per device

## 🔄 Migration Process

### What Gets Migrated

#### User Data
- Personal information (nama, email, telepon, alamat, tanggal lahir)
- Moved from `AsyncStorage` to `users` table

#### Target Data
- Savings goals (target amount, date, package type)
- Moved from `AsyncStorage` to `targets` table

#### Tabungan Data
- Current savings total
- Moved from `AsyncStorage` to `tabungan_data` table

#### Setoran History
- All deposit records
- Moved from `AsyncStorage` to `setoran_history` table

#### App Settings
- User preferences (theme, notifications, language)
- Moved from `AsyncStorage` to `app_settings` table

### Migration Flow

```mermaid
graph TD
    A[App Launch] --> B{Migration Needed?}
    B -->|Yes| C[Check Auth Status]
    B -->|No| H[Continue Normal Flow]
    C --> D{User Authenticated?}
    D -->|Yes| E[Migrate Data]
    D -->|No| F[Prompt Login/Register]
    F --> E
    E --> G[Mark Migration Complete]
    G --> H
```

## 🛠️ Technical Implementation

### Migration Status Tracking

The app tracks migration status in `AsyncStorage`:

```typescript
interface MigrationStatus {
  completed: boolean;
  version: string;
  timestamp: string;
  migratedData: {
    user: boolean;
    target: boolean;
    tabungan: boolean;
    settings: boolean;
  };
}
```

### Migration Functions

Located in `utils/migration.ts`:

- `checkMigrationStatus()`: Check if migration is needed
- `migrateUserData()`: Migrate user profile
- `migrateTargetData()`: Migrate savings targets
- `migrateTabunganData()`: Migrate savings totals and history
- `migrateAppSettings()`: Migrate user preferences
- `completeMigration()`: Mark migration as complete

## 🚀 Setup Instructions

### 1. Database Setup
First, ensure your Supabase database is set up:

```bash
# Run the database schema
# Copy database/schema.sql to Supabase SQL Editor and execute
```

### 2. Environment Configuration
Update your environment variables:

```bash
EXPO_PUBLIC_SUPABASE_URL=your_supabase_url
EXPO_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
```

### 3. Test Migration
Use the setup script to verify everything works:

```bash
npx ts-node scripts/setup-database.ts
```

## 📱 User Experience

### For New Users
- No migration needed
- Data goes directly to Supabase
- Seamless experience

### For Existing Users
- Migration happens automatically on first launch
- Brief loading screen during migration
- All data preserved
- No action required from user

### Migration UI Flow

1. **App Launch**: Check migration status
2. **Authentication**: Ensure user is logged in
3. **Migration Screen**: Show progress during migration
4. **Completion**: Continue to main app
5. **Backup**: Local data kept as backup

## 🔍 Testing Migration

### Test Scenarios

#### 1. New Installation
```bash
# Clear app data
# Install app
# Register new user
# Verify data goes to Supabase
```

#### 2. Existing User Migration
```bash
# Have existing local data
# Update app with Supabase
# Launch app
# Verify migration completes
# Check data in Supabase
```

#### 3. Failed Migration Recovery
```bash
# Simulate migration failure
# Restart app
# Verify retry mechanism works
# Check data integrity
```

### Verification Steps

1. **Before Migration**:
   - Export local data for comparison
   - Note total savings amount
   - Count deposit records

2. **After Migration**:
   - Verify all data in Supabase
   - Check totals match
   - Confirm deposit history complete

3. **App Functionality**:
   - Test adding new deposits
   - Verify automatic calculations
   - Check settings persistence

## 🚨 Troubleshooting

### Common Issues

#### Migration Fails
**Symptoms**: Error during migration, data not transferred
**Solutions**:
- Check internet connection
- Verify Supabase credentials
- Ensure user is authenticated
- Check Supabase logs for errors

#### Partial Migration
**Symptoms**: Some data migrated, some missing
**Solutions**:
- Check migration status in AsyncStorage
- Re-run specific migration functions
- Verify RLS policies allow data insertion

#### Data Mismatch
**Symptoms**: Totals don't match after migration
**Solutions**:
- Check setoran_history records
- Verify trigger functions are working
- Recalculate totals manually

### Debug Tools

#### Check Migration Status
```typescript
import { checkMigrationStatus } from '@/utils/migration';

const status = await checkMigrationStatus();
console.log('Migration status:', status);
```

#### Force Re-migration
```typescript
import AsyncStorage from '@react-native-async-storage/async-storage';

// Clear migration status (use with caution)
await AsyncStorage.removeItem('migration_status');
```

#### Verify Data Integrity
```typescript
import { combinedService } from '@/utils/supabase';

const userData = await combinedService.getAllUserData(userId);
console.log('User data:', userData);
```

## 📊 Migration Monitoring

### Success Metrics
- Migration completion rate
- Data integrity verification
- User experience feedback
- Performance impact

### Logging
The migration process logs:
- Start/end times
- Data volumes migrated
- Any errors encountered
- Success/failure status

## 🔄 Rollback Plan

If migration issues occur:

1. **Immediate**: Local data remains as backup
2. **App Function**: Can temporarily disable Supabase
3. **Data Recovery**: Local data can be restored
4. **User Impact**: Minimal disruption

### Emergency Rollback
```typescript
// Disable Supabase temporarily
const USE_SUPABASE = false;

// App will fall back to local storage
```

## 📈 Post-Migration

### Cleanup (Optional)
After successful migration and verification:

```typescript
// Remove local backup data (optional)
await AsyncStorage.multiRemove([
  'user_data_backup',
  'target_data_backup',
  'tabungan_data_backup',
  'settings_backup'
]);
```

### Monitoring
- Monitor app performance
- Check error rates
- Verify data synchronization
- User feedback collection

## ✅ Migration Checklist

- [ ] Database schema deployed
- [ ] Environment variables configured
- [ ] Migration functions tested
- [ ] User authentication working
- [ ] RLS policies configured
- [ ] Backup strategy in place
- [ ] Rollback plan ready
- [ ] Monitoring setup
- [ ] User communication prepared

---

**Important**: Always test migration thoroughly in development before deploying to production users.
