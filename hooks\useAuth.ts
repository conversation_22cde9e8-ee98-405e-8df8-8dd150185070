import { useState } from 'react';
import { useAuth } from '../contexts/AuthContext';

// Hook for login form
export const useLogin = () => {
  const { signIn } = useAuth();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const login = async (email: string, password: string) => {
    setLoading(true);
    setError(null);
    
    try {
      await signIn(email, password);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Login failed');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  return { login, loading, error, clearError: () => setError(null) };
};

// Hook for registration form
export const useRegister = () => {
  const { signUp } = useAuth();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const register = async (
    email: string, 
    password: string, 
    userData?: {
      nama: string;
      telepon?: string;
      alamat?: string;
      tanggal_lahir?: string;
    }
  ) => {
    setLoading(true);
    setError(null);
    
    try {
      await signUp(email, password, userData);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Registration failed');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  return { register, loading, error, clearError: () => setError(null) };
};

// Hook for logout
export const useLogout = () => {
  const { signOut } = useAuth();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const logout = async () => {
    setLoading(true);
    setError(null);
    
    try {
      await signOut();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Logout failed');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  return { logout, loading, error };
};

// Hook for password reset
export const usePasswordReset = () => {
  const { resetPassword } = useAuth();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const sendResetEmail = async (email: string) => {
    setLoading(true);
    setError(null);
    setSuccess(false);
    
    try {
      await resetPassword(email);
      setSuccess(true);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Password reset failed');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  return { 
    sendResetEmail, 
    loading, 
    error, 
    success,
    clearError: () => setError(null),
    clearSuccess: () => setSuccess(false)
  };
};

// Hook for password update
export const usePasswordUpdate = () => {
  const { updatePassword } = useAuth();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const changePassword = async (newPassword: string) => {
    setLoading(true);
    setError(null);
    setSuccess(false);
    
    try {
      await updatePassword(newPassword);
      setSuccess(true);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Password update failed');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  return { 
    changePassword, 
    loading, 
    error, 
    success,
    clearError: () => setError(null),
    clearSuccess: () => setSuccess(false)
  };
};

// Hook for user profile update
export const useProfileUpdate = () => {
  const { updateUserMetadata } = useAuth();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const updateProfile = async (metadata: Record<string, any>) => {
    setLoading(true);
    setError(null);
    setSuccess(false);
    
    try {
      await updateUserMetadata(metadata);
      setSuccess(true);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Profile update failed');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  return { 
    updateProfile, 
    loading, 
    error, 
    success,
    clearError: () => setError(null),
    clearSuccess: () => setSuccess(false)
  };
};
