import React, { useEffect } from 'react';
import { ViewProps } from 'react-native';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withTiming,
  withDelay,
  withSpring,
  Easing,
} from 'react-native-reanimated';

interface FadeInViewProps extends ViewProps {
  delay?: number;
  duration?: number;
  slideDistance?: number;
  slideDirection?: 'up' | 'down' | 'left' | 'right';
  springAnimation?: boolean;
  children: React.ReactNode;
}

export function FadeInView({
  delay = 0,
  duration = 600,
  slideDistance = 20,
  slideDirection = 'up',
  springAnimation = false,
  style,
  children,
  ...props
}: FadeInViewProps) {
  const opacity = useSharedValue(0);
  const translateX = useSharedValue(
    slideDirection === 'left' ? -slideDistance : 
    slideDirection === 'right' ? slideDistance : 0
  );
  const translateY = useSharedValue(
    slideDirection === 'up' ? slideDistance : 
    slideDirection === 'down' ? -slideDistance : 0
  );

  const animatedStyle = useAnimatedStyle(() => {
    return {
      opacity: opacity.value,
      transform: [
        { translateX: translateX.value },
        { translateY: translateY.value },
      ],
    };
  });

  useEffect(() => {
    const animationConfig = springAnimation
      ? {
          damping: 15,
          stiffness: 100,
          mass: 1,
        }
      : {
          duration,
          easing: Easing.out(Easing.cubic),
        };

    opacity.value = withDelay(
      delay,
      springAnimation 
        ? withSpring(1, animationConfig)
        : withTiming(1, animationConfig)
    );
    
    translateX.value = withDelay(
      delay,
      springAnimation 
        ? withSpring(0, animationConfig)
        : withTiming(0, animationConfig)
    );
    
    translateY.value = withDelay(
      delay,
      springAnimation 
        ? withSpring(0, animationConfig)
        : withTiming(0, animationConfig)
    );
  }, []);

  return (
    <Animated.View style={[animatedStyle, style]} {...props}>
      {children}
    </Animated.View>
  );
}

// Convenience components for different animation types
export function SlideUpFadeIn(props: Omit<FadeInViewProps, 'slideDirection'>) {
  return <FadeInView slideDirection="up" {...props} />;
}

export function SlideDownFadeIn(props: Omit<FadeInViewProps, 'slideDirection'>) {
  return <FadeInView slideDirection="down" {...props} />;
}

export function SlideLeftFadeIn(props: Omit<FadeInViewProps, 'slideDirection'>) {
  return <FadeInView slideDirection="left" {...props} />;
}

export function SlideRightFadeIn(props: Omit<FadeInViewProps, 'slideDirection'>) {
  return <FadeInView slideDirection="right" {...props} />;
}

export function SpringFadeIn(props: Omit<FadeInViewProps, 'springAnimation'>) {
  return <FadeInView springAnimation {...props} />;
}
