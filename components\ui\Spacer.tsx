import React from 'react';
import { View, ViewProps } from 'react-native';
import { Spacing } from '@/constants/Colors';

interface SpacerProps extends ViewProps {
  size?: 'xs' | 'sm' | 'md' | 'base' | 'lg' | 'xl' | '2xl' | '3xl' | '4xl' | '5xl' | '6xl';
  horizontal?: boolean;
  vertical?: boolean;
}

export function Spacer({ 
  size = 'base',
  horizontal = false,
  vertical = false,
  style,
  ...props 
}: SpacerProps) {
  const getSpacing = () => {
    switch (size) {
      case 'xs':
        return Spacing.xs;
      case 'sm':
        return Spacing.sm;
      case 'md':
        return Spacing.md;
      case 'base':
        return Spacing.base;
      case 'lg':
        return Spacing.lg;
      case 'xl':
        return Spacing.xl;
      case '2xl':
        return Spacing['2xl'];
      case '3xl':
        return Spacing['3xl'];
      case '4xl':
        return Spacing['4xl'];
      case '5xl':
        return Spacing['5xl'];
      case '6xl':
        return Spacing['6xl'];
      default:
        return Spacing.base;
    }
  };
  
  const spacing = getSpacing();
  
  const spacerStyle = {
    width: horizontal ? spacing : undefined,
    height: vertical ? spacing : (!horizontal ? spacing : undefined),
  };
  
  return (
    <View 
      style={[spacerStyle, style]} 
      {...props} 
    />
  );
}

// Convenience components for common spacing needs
export const VerticalSpacer = (props: Omit<SpacerProps, 'vertical'>) => (
  <Spacer vertical {...props} />
);

export const HorizontalSpacer = (props: Omit<SpacerProps, 'horizontal'>) => (
  <Spacer horizontal {...props} />
);
