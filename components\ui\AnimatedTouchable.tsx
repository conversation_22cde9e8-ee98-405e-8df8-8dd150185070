import React from 'react';
import { TouchableOpacity, TouchableOpacityProps } from 'react-native';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  withTiming,
  runOnJS,
} from 'react-native-reanimated';

const AnimatedTouchableOpacity = Animated.createAnimatedComponent(TouchableOpacity);

interface AnimatedTouchableProps extends TouchableOpacityProps {
  scaleOnPress?: boolean;
  scaleValue?: number;
  hapticFeedback?: boolean;
  children: React.ReactNode;
}

export function AnimatedTouchable({
  scaleOnPress = true,
  scaleValue = 0.95,
  hapticFeedback = true,
  onPressIn,
  onPressOut,
  style,
  children,
  ...props
}: AnimatedTouchableProps) {
  const scale = useSharedValue(1);
  const opacity = useSharedValue(1);

  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: scale.value }],
      opacity: opacity.value,
    };
  });

  const handlePressIn = (event: any) => {
    if (scaleOnPress) {
      scale.value = withSpring(scaleValue, {
        damping: 15,
        stiffness: 300,
      });
    }
    
    opacity.value = withTiming(0.8, { duration: 100 });
    
    if (hapticFeedback) {
      // Add haptic feedback here if needed
    }
    
    onPressIn?.(event);
  };

  const handlePressOut = (event: any) => {
    if (scaleOnPress) {
      scale.value = withSpring(1, {
        damping: 15,
        stiffness: 300,
      });
    }
    
    opacity.value = withTiming(1, { duration: 150 });
    
    onPressOut?.(event);
  };

  return (
    <AnimatedTouchableOpacity
      style={[animatedStyle, style]}
      onPressIn={handlePressIn}
      onPressOut={handlePressOut}
      activeOpacity={1}
      {...props}
    >
      {children}
    </AnimatedTouchableOpacity>
  );
}

// Convenience component for cards that should have subtle press animations
export function AnimatedCard({
  children,
  ...props
}: Omit<AnimatedTouchableProps, 'scaleValue'>) {
  return (
    <AnimatedTouchable scaleValue={0.98} {...props}>
      {children}
    </AnimatedTouchable>
  );
}

// Convenience component for buttons with more pronounced animations
export function AnimatedButton({
  children,
  ...props
}: Omit<AnimatedTouchableProps, 'scaleValue'>) {
  return (
    <AnimatedTouchable scaleValue={0.92} {...props}>
      {children}
    </AnimatedTouchable>
  );
}
