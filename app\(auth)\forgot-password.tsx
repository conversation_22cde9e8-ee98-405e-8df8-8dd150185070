import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  ActivityIndicator,
} from 'react-native';
import { Link, router } from 'expo-router';
import { usePasswordReset } from '../../hooks/useAuth';

export default function ForgotPasswordScreen() {
  const [email, setEmail] = useState('');
  const { sendResetEmail, loading, error, success, clearError, clearSuccess } = usePasswordReset();

  const handleResetPassword = async () => {
    if (!email.trim()) {
      Alert.alert('Error', 'Please enter your email address');
      return;
    }

    if (!isValidEmail(email)) {
      Alert.alert('Error', 'Please enter a valid email address');
      return;
    }

    try {
      await sendResetEmail(email.trim());
    } catch (err) {
      Alert.alert('Reset Failed', error || 'An error occurred while sending reset email');
    }
  };

  const isValidEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const handleBackToLogin = () => {
    clearError();
    clearSuccess();
    router.back();
  };

  return (
    <KeyboardAvoidingView 
      style={styles.container} 
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView contentContainerStyle={styles.scrollContainer}>
        <View style={styles.header}>
          <Text style={styles.title}>Lupa Password?</Text>
          <Text style={styles.subtitle}>
            Masukkan email Anda dan kami akan mengirimkan link untuk reset password
          </Text>
        </View>

        <View style={styles.form}>
          {!success ? (
            <>
              <View style={styles.inputContainer}>
                <Text style={styles.label}>Email</Text>
                <TextInput
                  style={styles.input}
                  value={email}
                  onChangeText={(text) => {
                    setEmail(text);
                    if (error) clearError();
                  }}
                  placeholder="Masukkan email Anda"
                  keyboardType="email-address"
                  autoCapitalize="none"
                  autoCorrect={false}
                />
              </View>

              {error && (
                <View style={styles.errorContainer}>
                  <Text style={styles.errorText}>{error}</Text>
                </View>
              )}

              <TouchableOpacity
                style={[styles.resetButton, loading && styles.resetButtonDisabled]}
                onPress={handleResetPassword}
                disabled={loading}
              >
                {loading ? (
                  <ActivityIndicator color="#ffffff" />
                ) : (
                  <Text style={styles.resetButtonText}>Kirim Link Reset</Text>
                )}
              </TouchableOpacity>
            </>
          ) : (
            <View style={styles.successContainer}>
              <Text style={styles.successIcon}>✅</Text>
              <Text style={styles.successTitle}>Email Terkirim!</Text>
              <Text style={styles.successText}>
                Kami telah mengirimkan link reset password ke email Anda. 
                Silakan cek inbox dan ikuti instruksi untuk reset password.
              </Text>
              <Text style={styles.successNote}>
                Tidak menerima email? Cek folder spam atau coba kirim ulang setelah beberapa menit.
              </Text>
            </View>
          )}

          <View style={styles.linkContainer}>
            <TouchableOpacity onPress={handleBackToLogin}>
              <Text style={styles.linkText}>← Kembali ke Login</Text>
            </TouchableOpacity>
          </View>
        </View>

        {success && (
          <View style={styles.footer}>
            <TouchableOpacity
              style={styles.resendButton}
              onPress={() => {
                clearSuccess();
                setEmail('');
              }}
            >
              <Text style={styles.resendButtonText}>Kirim Ulang Email</Text>
            </TouchableOpacity>
          </View>
        )}
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  scrollContainer: {
    flexGrow: 1,
    justifyContent: 'center',
    padding: 20,
  },
  header: {
    alignItems: 'center',
    marginBottom: 40,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#194a7a',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#7593af',
    textAlign: 'center',
    lineHeight: 22,
  },
  form: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 24,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  inputContainer: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: '#194a7a',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: '#d1dbe4',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: '#f8f9fa',
  },
  errorContainer: {
    backgroundColor: '#fee',
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
  },
  errorText: {
    color: '#d32f2f',
    fontSize: 14,
    textAlign: 'center',
  },
  resetButton: {
    backgroundColor: '#194a7a',
    borderRadius: 8,
    padding: 16,
    alignItems: 'center',
    marginTop: 8,
  },
  resetButtonDisabled: {
    backgroundColor: '#a3b7ca',
  },
  resetButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },
  successContainer: {
    alignItems: 'center',
    paddingVertical: 20,
  },
  successIcon: {
    fontSize: 48,
    marginBottom: 16,
  },
  successTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#194a7a',
    marginBottom: 12,
  },
  successText: {
    fontSize: 16,
    color: '#7593af',
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: 16,
  },
  successNote: {
    fontSize: 14,
    color: '#a3b7ca',
    textAlign: 'center',
    fontStyle: 'italic',
  },
  linkContainer: {
    alignItems: 'center',
    marginTop: 16,
  },
  linkText: {
    color: '#476f95',
    fontSize: 14,
  },
  footer: {
    alignItems: 'center',
    marginTop: 24,
  },
  resendButton: {
    borderWidth: 1,
    borderColor: '#194a7a',
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 24,
  },
  resendButtonText: {
    color: '#194a7a',
    fontSize: 14,
    fontWeight: '600',
  },
});
