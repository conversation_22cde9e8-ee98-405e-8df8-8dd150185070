import { DUMMY_TABUNGAN_DATA } from '@/data/dummyData';
import { SetoranItem, TabunganData, tabunganStorage } from '@/utils/storage';
import { useEffect, useState } from 'react';

export function useTabunganData() {
  const [tabunganData, setTabunganData] = useState<TabunganData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load data on mount
  useEffect(() => {
    loadTabunganData();
  }, []);

  const loadTabunganData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Try to load from storage first
      let data = await tabunganStorage.getTabunganData();
      
      // If no data in storage, use dummy data
      if (!data) {
        data = DUMMY_TABUNGAN_DATA;
        await tabunganStorage.saveTabunganData(data);
      }
      
      setTabunganData(data);
    } catch (err) {
      setError('Gagal memuat data tabungan');
      console.error('Error loading tabungan data:', err);
    } finally {
      setLoading(false);
    }
  };

  const addSetoran = async (setoran: Omit<SetoranItem, 'id'>) => {
    try {
      setError(null);

      // Import Supabase storage functions
      const { tabunganStorage: supabaseTabunganStorage } = await import('@/utils/supabaseStorage');

      // Add to Supabase
      await supabaseTabunganStorage.addSetoran(setoran);

      // Reload data
      await loadTabunganData();

      return { success: true };
    } catch (err) {
      const errorMessage = 'Gagal menambah setoran';
      setError(errorMessage);
      console.error('Error adding setoran:', err);
      return { success: false, error: errorMessage };
    }
  };

  const updateTabunganData = async (newData: Partial<TabunganData>) => {
    try {
      setError(null);

      if (!tabunganData) return { success: false, error: 'Data tidak tersedia' };

      // Import Supabase storage functions
      const { tabunganStorage: supabaseTabunganStorage } = await import('@/utils/supabaseStorage');

      const updatedData = { ...tabunganData, ...newData };
      await supabaseTabunganStorage.saveTabunganData(updatedData);

      setTabunganData(updatedData);
      return { success: true };
    } catch (err) {
      const errorMessage = 'Gagal memperbarui data tabungan';
      setError(errorMessage);
      console.error('Error updating tabungan data:', err);
      return { success: false, error: errorMessage };
    }
  };

  const refreshData = () => {
    loadTabunganData();
  };

  // Calculate statistics
  const getStatistics = () => {
    if (!tabunganData) return null;

    const { totalTabungan, riwayatSetoran } = tabunganData;
    
    // Calculate average setoran
    const averageSetoran = riwayatSetoran.length > 0 
      ? totalTabungan / riwayatSetoran.length 
      : 0;

    // Calculate monthly average (last 6 months)
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);
    
    const recentSetoran = riwayatSetoran.filter(
      item => new Date(item.tanggal) >= sixMonthsAgo
    );
    
    const monthlyAverage = recentSetoran.length > 0
      ? recentSetoran.reduce((sum, item) => sum + item.jumlah, 0) / 6
      : 0;

    // Find largest and smallest setoran
    const amounts = riwayatSetoran.map(item => item.jumlah);
    const largestSetoran = amounts.length > 0 ? Math.max(...amounts) : 0;
    const smallestSetoran = amounts.length > 0 ? Math.min(...amounts) : 0;

    // Calculate frequency (setoran per month)
    const firstSetoran = riwayatSetoran[riwayatSetoran.length - 1];
    const monthsSinceFirst = firstSetoran 
      ? Math.max(1, Math.ceil((Date.now() - new Date(firstSetoran.tanggal).getTime()) / (1000 * 60 * 60 * 24 * 30)))
      : 1;
    
    const frequency = riwayatSetoran.length / monthsSinceFirst;

    return {
      totalTabungan,
      totalSetoran: riwayatSetoran.length,
      averageSetoran,
      monthlyAverage,
      largestSetoran,
      smallestSetoran,
      frequency,
      lastSetoran: riwayatSetoran[0] || null,
    };
  };

  return {
    tabunganData,
    loading,
    error,
    addSetoran,
    updateTabunganData,
    refreshData,
    statistics: getStatistics(),
  };
}
