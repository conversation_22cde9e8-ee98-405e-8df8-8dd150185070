{"expo": {"name": "tabungan", "slug": "tabungan", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "tabungan", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}]], "experiments": {"typedRoutes": true}, "extra": {"router": {}, "eas": {"projectId": "9698275f-b91e-4e7f-865e-2ba538a4547e"}}, "owner": "v<PERSON><PERSON><PERSON><PERSON>"}}