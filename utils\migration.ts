import AsyncStorage from '@react-native-async-storage/async-storage';
import { STORAGE_KEYS, UserData, TargetData, TabunganData, AppSettings } from './storage';
import { initializeUserData, userStorage as supabaseUserStorage, targetStorage as supabaseTargetStorage, tabunganStorage as supabaseTabunganStorage, settingsStorage as supabaseSettingsStorage } from './supabaseStorage';

// Migration status tracking
const MIGRATION_KEY = 'migration_status';

interface MigrationStatus {
  completed: boolean;
  version: string;
  timestamp: string;
  migratedData: {
    user: boolean;
    target: boolean;
    tabungan: boolean;
    settings: boolean;
  };
}

// Check if migration has been completed
export const isMigrationCompleted = async (): Promise<boolean> => {
  try {
    const migrationStatus = await AsyncStorage.getItem(MIGRATION_KEY);
    if (!migrationStatus) return false;
    
    const status: MigrationStatus = JSON.parse(migrationStatus);
    return status.completed;
  } catch (error) {
    console.error('Error checking migration status:', error);
    return false;
  }
};

// Get existing AsyncStorage data
const getAsyncStorageData = async () => {
  try {
    const keys = Object.values(STORAGE_KEYS);
    const result = await AsyncStorage.multiGet(keys);
    const data: Record<string, any> = {};
    
    result.forEach(([key, value]) => {
      if (value) {
        try {
          data[key] = JSON.parse(value);
        } catch {
          data[key] = value;
        }
      }
    });
    
    return data;
  } catch (error) {
    console.error('Error getting AsyncStorage data:', error);
    return {};
  }
};

// Migrate user data
const migrateUserData = async (asyncData: Record<string, any>): Promise<boolean> => {
  try {
    const userData = asyncData[STORAGE_KEYS.USER_DATA] as UserData;
    const targetData = asyncData[STORAGE_KEYS.TARGET_DATA] as TargetData;
    
    if (userData) {
      // Initialize user with Supabase (this creates user, tabungan_data, and settings)
      await initializeUserData(userData, targetData);
      console.log('✅ User data migrated successfully');
      return true;
    }
    
    return false;
  } catch (error) {
    console.error('❌ Error migrating user data:', error);
    return false;
  }
};

// Migrate tabungan data (setoran history)
const migrateTabunganData = async (asyncData: Record<string, any>): Promise<boolean> => {
  try {
    const tabunganData = asyncData[STORAGE_KEYS.TABUNGAN_DATA] as TabunganData;
    
    if (tabunganData && tabunganData.riwayatSetoran.length > 0) {
      // Add each setoran to Supabase
      for (const setoran of tabunganData.riwayatSetoran) {
        await supabaseTabunganStorage.addSetoran({
          tanggal: setoran.tanggal,
          jumlah: setoran.jumlah,
          keterangan: setoran.keterangan,
        });
      }
      console.log(`✅ Migrated ${tabunganData.riwayatSetoran.length} setoran records`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error('❌ Error migrating tabungan data:', error);
    return false;
  }
};

// Migrate app settings
const migrateAppSettings = async (asyncData: Record<string, any>): Promise<boolean> => {
  try {
    const settings = asyncData[STORAGE_KEYS.APP_SETTINGS] as AppSettings;
    
    if (settings) {
      await supabaseSettingsStorage.saveSettings(settings);
      console.log('✅ App settings migrated successfully');
      return true;
    }
    
    return false;
  } catch (error) {
    console.error('❌ Error migrating app settings:', error);
    return false;
  }
};

// Mark migration as completed
const markMigrationCompleted = async (migratedData: MigrationStatus['migratedData']): Promise<void> => {
  try {
    const migrationStatus: MigrationStatus = {
      completed: true,
      version: '1.0.0',
      timestamp: new Date().toISOString(),
      migratedData,
    };
    
    await AsyncStorage.setItem(MIGRATION_KEY, JSON.stringify(migrationStatus));
    console.log('✅ Migration marked as completed');
  } catch (error) {
    console.error('❌ Error marking migration as completed:', error);
  }
};

// Main migration function
export const migrateToSupabase = async (): Promise<{
  success: boolean;
  message: string;
  details: MigrationStatus['migratedData'];
}> => {
  console.log('🔄 Starting migration from AsyncStorage to Supabase...');
  
  try {
    // Check if migration already completed
    const alreadyMigrated = await isMigrationCompleted();
    if (alreadyMigrated) {
      return {
        success: true,
        message: 'Migration already completed',
        details: { user: true, target: true, tabungan: true, settings: true },
      };
    }
    
    // Get existing AsyncStorage data
    const asyncData = await getAsyncStorageData();
    
    if (Object.keys(asyncData).length === 0) {
      console.log('ℹ️ No AsyncStorage data found to migrate');
      return {
        success: true,
        message: 'No data to migrate',
        details: { user: false, target: false, tabungan: false, settings: false },
      };
    }
    
    console.log(`📊 Found data in ${Object.keys(asyncData).length} AsyncStorage keys`);
    
    // Perform migrations
    const migrationResults = {
      user: await migrateUserData(asyncData),
      target: true, // Target is migrated with user data
      tabungan: await migrateTabunganData(asyncData),
      settings: await migrateAppSettings(asyncData),
    };
    
    // Mark migration as completed
    await markMigrationCompleted(migrationResults);
    
    const successCount = Object.values(migrationResults).filter(Boolean).length;
    const totalCount = Object.keys(migrationResults).length;
    
    console.log(`🎉 Migration completed: ${successCount}/${totalCount} data types migrated`);
    
    return {
      success: true,
      message: `Migration completed successfully. ${successCount}/${totalCount} data types migrated.`,
      details: migrationResults,
    };
    
  } catch (error) {
    console.error('💥 Migration failed:', error);
    return {
      success: false,
      message: `Migration failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      details: { user: false, target: false, tabungan: false, settings: false },
    };
  }
};

// Clear AsyncStorage after successful migration (optional)
export const clearAsyncStorageAfterMigration = async (): Promise<void> => {
  try {
    const migrationCompleted = await isMigrationCompleted();
    if (!migrationCompleted) {
      console.log('⚠️ Migration not completed, skipping AsyncStorage cleanup');
      return;
    }
    
    // Clear only the app data, keep migration status
    const keys = Object.values(STORAGE_KEYS);
    await AsyncStorage.multiRemove(keys);
    console.log('🧹 AsyncStorage cleaned up after successful migration');
  } catch (error) {
    console.error('❌ Error clearing AsyncStorage:', error);
  }
};

// Reset migration status (for testing purposes)
export const resetMigrationStatus = async (): Promise<void> => {
  try {
    await AsyncStorage.removeItem(MIGRATION_KEY);
    console.log('🔄 Migration status reset');
  } catch (error) {
    console.error('❌ Error resetting migration status:', error);
  }
};

// Get migration status details
export const getMigrationStatus = async (): Promise<MigrationStatus | null> => {
  try {
    const migrationStatus = await AsyncStorage.getItem(MIGRATION_KEY);
    return migrationStatus ? JSON.parse(migrationStatus) : null;
  } catch (error) {
    console.error('Error getting migration status:', error);
    return null;
  }
};
