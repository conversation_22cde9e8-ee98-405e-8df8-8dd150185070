import { useAuth } from '@/contexts/AuthContext';
import { Redirect } from 'expo-router';
import React from 'react';
import { ActivityIndicator, StyleSheet, View } from 'react-native';

export default function Index() {
  const { user, loading } = useAuth();

  // Show loading spinner while checking authentication
  if (loading) {
    console.log('Index: Loading authentication state...');
    return (
      <View style={styles.container}>
        <ActivityIndicator size="large" color="#194a7a" />
      </View>
    );
  }

  // Redirect based on authentication status
  if (user) {
    console.log('Index: User authenticated, redirecting to /(tabs)');
    return <Redirect href="/(tabs)" />;
  } else {
    console.log('Index: User not authenticated, redirecting to /(auth)/login');
    return <Redirect href="/(auth)/login" />;
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
  },
});
