-- Tabungan Haji App - Supabase Database Schema
-- This schema creates all necessary tables, indexes, triggers, and RLS policies
-- for the Hajj savings application

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =============================================
-- TABLES
-- =============================================

-- Users table (extends auth.users)
CREATE TABLE IF NOT EXISTS public.users (
    id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    nama TEXT,
    email TEXT NOT NULL,
    telepon TEXT,
    alamat TEXT,
    tanggal_lahir DATE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Savings targets table
CREATE TABLE IF NOT EXISTS public.targets (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    target_biaya BIGINT NOT NULL CHECK (target_biaya > 0),
    tanggal_target DATE NOT NULL,
    paket_haji TEXT NOT NULL CHECK (paket_haji IN ('reguler', 'plus', 'khusus')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id) -- One target per user
);

-- Tabungan (savings) data table
CREATE TABLE IF NOT EXISTS public.tabungan_data (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    total_tabungan BIGINT NOT NULL DEFAULT 0 CHECK (total_tabungan >= 0),
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id) -- One tabungan record per user
);

-- Setoran (deposit) history table
CREATE TABLE IF NOT EXISTS public.setoran_history (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    tanggal DATE NOT NULL DEFAULT CURRENT_DATE,
    jumlah BIGINT NOT NULL CHECK (jumlah > 0),
    keterangan TEXT NOT NULL DEFAULT '',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- App settings table
CREATE TABLE IF NOT EXISTS public.app_settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    theme TEXT NOT NULL DEFAULT 'system' CHECK (theme IN ('light', 'dark', 'system')),
    notifications BOOLEAN NOT NULL DEFAULT true,
    language TEXT NOT NULL DEFAULT 'id' CHECK (language IN ('id', 'en')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id) -- One settings record per user
);

-- =============================================
-- INDEXES
-- =============================================

-- Users table indexes
CREATE INDEX IF NOT EXISTS idx_users_email ON public.users(email);
CREATE INDEX IF NOT EXISTS idx_users_created_at ON public.users(created_at);

-- Targets table indexes
CREATE INDEX IF NOT EXISTS idx_targets_user_id ON public.targets(user_id);
CREATE INDEX IF NOT EXISTS idx_targets_tanggal_target ON public.targets(tanggal_target);

-- Tabungan data table indexes
CREATE INDEX IF NOT EXISTS idx_tabungan_data_user_id ON public.tabungan_data(user_id);
CREATE INDEX IF NOT EXISTS idx_tabungan_data_last_updated ON public.tabungan_data(last_updated);

-- Setoran history table indexes
CREATE INDEX IF NOT EXISTS idx_setoran_history_user_id ON public.setoran_history(user_id);
CREATE INDEX IF NOT EXISTS idx_setoran_history_tanggal ON public.setoran_history(tanggal);
CREATE INDEX IF NOT EXISTS idx_setoran_history_user_tanggal ON public.setoran_history(user_id, tanggal DESC);

-- App settings table indexes
CREATE INDEX IF NOT EXISTS idx_app_settings_user_id ON public.app_settings(user_id);

-- =============================================
-- FUNCTIONS
-- =============================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Function to calculate total tabungan from setoran history
CREATE OR REPLACE FUNCTION calculate_total_tabungan(p_user_id UUID)
RETURNS BIGINT AS $$
DECLARE
    total BIGINT;
BEGIN
    SELECT COALESCE(SUM(jumlah), 0) INTO total
    FROM public.setoran_history
    WHERE user_id = p_user_id;
    
    RETURN total;
END;
$$ LANGUAGE plpgsql;

-- Function to update tabungan total when setoran changes
CREATE OR REPLACE FUNCTION update_tabungan_total()
RETURNS TRIGGER AS $$
DECLARE
    affected_user_id UUID;
    new_total BIGINT;
BEGIN
    -- Determine which user_id was affected
    IF TG_OP = 'DELETE' THEN
        affected_user_id := OLD.user_id;
    ELSE
        affected_user_id := NEW.user_id;
    END IF;
    
    -- Calculate new total
    new_total := calculate_total_tabungan(affected_user_id);
    
    -- Update tabungan_data
    UPDATE public.tabungan_data 
    SET 
        total_tabungan = new_total,
        last_updated = NOW(),
        updated_at = NOW()
    WHERE user_id = affected_user_id;
    
    -- If no tabungan_data record exists, create one
    IF NOT FOUND THEN
        INSERT INTO public.tabungan_data (user_id, total_tabungan)
        VALUES (affected_user_id, new_total);
    END IF;
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Function to handle new user registration
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    -- Insert user record
    INSERT INTO public.users (id, email, nama)
    VALUES (
        NEW.id,
        NEW.email,
        COALESCE(NEW.raw_user_meta_data->>'nama', NEW.raw_user_meta_data->>'name')
    );
    
    -- Initialize tabungan_data
    INSERT INTO public.tabungan_data (user_id, total_tabungan)
    VALUES (NEW.id, 0);
    
    -- Initialize app_settings
    INSERT INTO public.app_settings (user_id)
    VALUES (NEW.id);
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =============================================
-- TRIGGERS
-- =============================================

-- Trigger to update updated_at on users table
CREATE TRIGGER trigger_users_updated_at
    BEFORE UPDATE ON public.users
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Trigger to update updated_at on targets table
CREATE TRIGGER trigger_targets_updated_at
    BEFORE UPDATE ON public.targets
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Trigger to update updated_at on tabungan_data table
CREATE TRIGGER trigger_tabungan_data_updated_at
    BEFORE UPDATE ON public.tabungan_data
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Trigger to update updated_at on app_settings table
CREATE TRIGGER trigger_app_settings_updated_at
    BEFORE UPDATE ON public.app_settings
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Trigger to automatically update tabungan total when setoran changes
CREATE TRIGGER trigger_update_tabungan_total
    AFTER INSERT OR UPDATE OR DELETE ON public.setoran_history
    FOR EACH ROW
    EXECUTE FUNCTION update_tabungan_total();

-- Trigger to handle new user registration
CREATE TRIGGER trigger_handle_new_user
    AFTER INSERT ON auth.users
    FOR EACH ROW
    EXECUTE FUNCTION handle_new_user();

-- =============================================
-- ROW LEVEL SECURITY (RLS) POLICIES
-- =============================================

-- Enable RLS on all tables
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.targets ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.tabungan_data ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.setoran_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.app_settings ENABLE ROW LEVEL SECURITY;

-- Users table policies
CREATE POLICY "Users can view own profile" ON public.users
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON public.users
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile" ON public.users
    FOR INSERT WITH CHECK (auth.uid() = id);

-- Targets table policies
CREATE POLICY "Users can view own targets" ON public.targets
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own targets" ON public.targets
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own targets" ON public.targets
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own targets" ON public.targets
    FOR DELETE USING (auth.uid() = user_id);

-- Tabungan data table policies
CREATE POLICY "Users can view own tabungan data" ON public.tabungan_data
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own tabungan data" ON public.tabungan_data
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own tabungan data" ON public.tabungan_data
    FOR UPDATE USING (auth.uid() = user_id);

-- Setoran history table policies
CREATE POLICY "Users can view own setoran history" ON public.setoran_history
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own setoran history" ON public.setoran_history
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own setoran history" ON public.setoran_history
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own setoran history" ON public.setoran_history
    FOR DELETE USING (auth.uid() = user_id);

-- App settings table policies
CREATE POLICY "Users can view own settings" ON public.app_settings
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own settings" ON public.app_settings
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own settings" ON public.app_settings
    FOR UPDATE USING (auth.uid() = user_id);

-- =============================================
-- VIEWS FOR EASIER DATA ACCESS
-- =============================================

-- View to get user summary with target and tabungan info
CREATE OR REPLACE VIEW user_summary AS
SELECT
    u.id,
    u.nama,
    u.email,
    u.telepon,
    u.alamat,
    u.tanggal_lahir,
    u.created_at as user_created_at,
    t.target_biaya,
    t.tanggal_target,
    t.paket_haji,
    td.total_tabungan,
    td.last_updated as tabungan_last_updated,
    CASE
        WHEN t.target_biaya > 0 THEN
            ROUND((td.total_tabungan::DECIMAL / t.target_biaya::DECIMAL) * 100, 2)
        ELSE 0
    END as progress_percentage,
    CASE
        WHEN t.target_biaya > 0 THEN
            GREATEST(0, t.target_biaya - td.total_tabungan)
        ELSE 0
    END as remaining_amount,
    CASE
        WHEN t.tanggal_target IS NOT NULL THEN
            GREATEST(0, t.tanggal_target - CURRENT_DATE)
        ELSE NULL
    END as days_remaining
FROM public.users u
LEFT JOIN public.targets t ON u.id = t.user_id
LEFT JOIN public.tabungan_data td ON u.id = td.user_id;

-- View to get recent setoran history with running totals
CREATE OR REPLACE VIEW setoran_summary AS
SELECT
    sh.id,
    sh.user_id,
    sh.tanggal,
    sh.jumlah,
    sh.keterangan,
    sh.created_at,
    SUM(sh.jumlah) OVER (
        PARTITION BY sh.user_id
        ORDER BY sh.tanggal, sh.created_at
        ROWS UNBOUNDED PRECEDING
    ) as running_total
FROM public.setoran_history sh
ORDER BY sh.user_id, sh.tanggal DESC, sh.created_at DESC;

-- =============================================
-- SAMPLE DATA (OPTIONAL - FOR TESTING)
-- =============================================

-- Note: This sample data will only be inserted if the tables are empty
-- Remove this section in production

DO $$
BEGIN
    -- Only insert sample data if tables are empty
    IF NOT EXISTS (SELECT 1 FROM public.users LIMIT 1) THEN
        -- This would normally be handled by the auth trigger
        -- but included here for testing purposes

        RAISE NOTICE 'Sample data insertion skipped - use Supabase Auth for user creation';
    END IF;
END $$;

-- =============================================
-- HELPFUL QUERIES FOR MAINTENANCE
-- =============================================

-- Query to check user statistics
-- SELECT
--     COUNT(*) as total_users,
--     COUNT(t.id) as users_with_targets,
--     AVG(td.total_tabungan) as avg_savings,
--     SUM(td.total_tabungan) as total_savings
-- FROM public.users u
-- LEFT JOIN public.targets t ON u.id = t.user_id
-- LEFT JOIN public.tabungan_data td ON u.id = td.user_id;

-- Query to find users close to their target
-- SELECT
--     u.nama,
--     u.email,
--     t.target_biaya,
--     td.total_tabungan,
--     ROUND((td.total_tabungan::DECIMAL / t.target_biaya::DECIMAL) * 100, 2) as progress
-- FROM public.users u
-- JOIN public.targets t ON u.id = t.user_id
-- JOIN public.tabungan_data td ON u.id = td.user_id
-- WHERE td.total_tabungan::DECIMAL / t.target_biaya::DECIMAL >= 0.9
-- ORDER BY progress DESC;

-- =============================================
-- COMPLETION MESSAGE
-- =============================================

DO $$
BEGIN
    RAISE NOTICE '✅ Tabungan Haji database schema created successfully!';
    RAISE NOTICE '📋 Tables created: users, targets, tabungan_data, setoran_history, app_settings';
    RAISE NOTICE '🔒 RLS policies enabled for all tables';
    RAISE NOTICE '⚡ Triggers configured for automatic calculations';
    RAISE NOTICE '👀 Views created for easier data access';
    RAISE NOTICE '';
    RAISE NOTICE '🚀 Next steps:';
    RAISE NOTICE '1. Test the schema with your app';
    RAISE NOTICE '2. Run the setup-database.ts script to verify';
    RAISE NOTICE '3. Configure your Supabase environment variables';
    RAISE NOTICE '4. Test user registration and data flow';
END $$;
