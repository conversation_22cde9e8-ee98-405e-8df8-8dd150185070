// Comprehensive dummy data for the Tabungan Haji app

export const DUMMY_USER_DATA = {
  nama: '<PERSON>',
  email: '<EMAIL>',
  telepon: '081234567890',
  alamat: 'Jl. Masjid Agung No. 123, Kelurahan <PERSON>, Jakarta Pusat 10310',
  tanggalLahir: '1985-05-15',
};

export const DUMMY_TARGET_DATA = {
  targetBiaya: 35000000, // 35 juta
  tanggalTarget: '2026-12-31',
  paketHaji: 'reguler' as const,
};

// Generate realistic setoran history for the past 6 months
export const generateDummySetoranHistory = () => {
  const history = [];
  const today = new Date();
  
  // Base amounts for different types of setoran
  const setoranTypes = [
    { keterangan: 'Setoran rutin bulanan', baseAmount: 500000, variance: 0.1 },
    { keterangan: 'Setoran tambahan', baseAmount: 300000, variance: 0.3 },
    { keterangan: 'Bonus kerja', baseAmount: 1000000, variance: 0.2 },
    { keterangan: 'THR', baseAmount: 1500000, variance: 0.1 },
    { keterangan: 'Hasil investasi', baseAmount: 750000, variance: 0.4 },
    { keterangan: 'Tabungan lebaran', baseAmount: 600000, variance: 0.2 },
  ];

  let id = 1;
  
  // Generate setoran for the past 6 months
  for (let monthsAgo = 6; monthsAgo >= 0; monthsAgo--) {
    const date = new Date(today);
    date.setMonth(date.getMonth() - monthsAgo);
    
    // 1-3 setoran per month
    const setoranCount = Math.floor(Math.random() * 3) + 1;
    
    for (let i = 0; i < setoranCount; i++) {
      const setoranDate = new Date(date);
      setoranDate.setDate(Math.floor(Math.random() * 28) + 1); // Random day in month
      
      const typeIndex = Math.floor(Math.random() * setoranTypes.length);
      const setoranType = setoranTypes[typeIndex];
      
      // Add variance to amount
      const variance = (Math.random() - 0.5) * 2 * setoranType.variance;
      const amount = Math.round(setoranType.baseAmount * (1 + variance) / 50000) * 50000; // Round to nearest 50k
      
      history.push({
        id: id++,
        tanggal: setoranDate.toISOString().split('T')[0],
        jumlah: Math.max(amount, 50000), // Minimum 50k
        keterangan: setoranType.keterangan,
      });
    }
  }
  
  // Sort by date (newest first)
  return history.sort((a, b) => new Date(b.tanggal).getTime() - new Date(a.tanggal).getTime());
};

export const DUMMY_SETORAN_HISTORY = generateDummySetoranHistory();

export const DUMMY_TABUNGAN_DATA = {
  totalTabungan: DUMMY_SETORAN_HISTORY.reduce((total, item) => total + item.jumlah, 0),
  riwayatSetoran: DUMMY_SETORAN_HISTORY,
  lastUpdated: new Date().toISOString(),
};

// Biaya haji estimates (updated for 2024)
export const BIAYA_HAJI_2024 = {
  reguler: {
    biaya: 35000000,
    nama: 'Haji Reguler',
    deskripsi: 'Paket haji dengan fasilitas standar sesuai ketentuan pemerintah',
    fasilitas: [
      'Tiket pesawat PP Jakarta-Jeddah',
      'Visa haji',
      'Akomodasi hotel bintang 3-4',
      'Makan 3x sehari',
      'Transportasi bus AC',
      'Bimbingan manasik',
      'Perlengkapan haji dasar',
    ],
  },
  plus: {
    biaya: 50000000,
    nama: 'Haji Plus',
    deskripsi: 'Paket haji dengan fasilitas lebih baik dan nyaman',
    fasilitas: [
      'Tiket pesawat PP Jakarta-Jeddah (seat lebih lega)',
      'Visa haji',
      'Akomodasi hotel bintang 4-5',
      'Makan 3x sehari (menu variatif)',
      'Transportasi bus AC premium',
      'Bimbingan manasik intensif',
      'Perlengkapan haji lengkap',
      'Layanan kesehatan 24 jam',
    ],
  },
  khusus: {
    biaya: 75000000,
    nama: 'Haji Khusus',
    deskripsi: 'Paket haji premium dengan fasilitas eksklusif',
    fasilitas: [
      'Tiket pesawat PP Jakarta-Jeddah (business class)',
      'Visa haji',
      'Akomodasi hotel bintang 5 (dekat Masjidil Haram)',
      'Makan 3x sehari (buffet premium)',
      'Transportasi bus AC VIP',
      'Bimbingan manasik personal',
      'Perlengkapan haji premium',
      'Layanan kesehatan 24 jam',
      'Tour ziarah tambahan',
      'Laundry service',
    ],
  },
};

// Tips and educational content
export const TIPS_MENABUNG_HAJI = [
  {
    kategori: 'Perencanaan Keuangan',
    tips: [
      'Tentukan target waktu keberangkatan yang realistis',
      'Hitung kebutuhan biaya total termasuk dana darurat',
      'Buat anggaran bulanan khusus untuk tabungan haji',
      'Pisahkan rekening tabungan haji dari rekening lainnya',
    ],
  },
  {
    kategori: 'Strategi Menabung',
    tips: [
      'Mulai dengan jumlah kecil tapi konsisten',
      'Manfaatkan bonus, THR, atau pendapatan tambahan',
      'Kurangi pengeluaran yang tidak perlu',
      'Otomatisasi transfer ke tabungan haji setiap bulan',
    ],
  },
  {
    kategori: 'Investasi Syariah',
    tips: [
      'Pertimbangkan deposito syariah untuk dana jangka panjang',
      'Investasi reksa dana syariah untuk potensi return lebih tinggi',
      'Diversifikasi investasi untuk mengurangi risiko',
      'Konsultasi dengan ahli keuangan syariah',
    ],
  },
];

export const MOTIVATIONAL_QUOTES = [
  'Setiap rupiah yang ditabung adalah langkah menuju Baitullah',
  'Konsistensi kecil lebih baik dari niat besar tanpa tindakan',
  'Haji adalah investasi akhirat yang dimulai dari dunia',
  'Tabungan hari ini adalah doa yang terkabul di masa depan',
  'Setiap setoran adalah bukti keseriusan niat berhaji',
];

// Mock API responses for testing
export const MOCK_API_RESPONSES = {
  login: {
    success: true,
    data: {
      user: DUMMY_USER_DATA,
      token: 'mock_jwt_token_12345',
    },
  },
  getTabunganData: {
    success: true,
    data: DUMMY_TABUNGAN_DATA,
  },
  addSetoran: {
    success: true,
    message: 'Setoran berhasil ditambahkan',
  },
  updateProfile: {
    success: true,
    message: 'Profil berhasil diperbarui',
  },
};

// Test scenarios
export const TEST_SCENARIOS = {
  newUser: {
    userData: {
      nama: 'Siti Aminah',
      email: '<EMAIL>',
      telepon: '082345678901',
      alamat: 'Jl. Sudirman No. 456, Surabaya',
      tanggalLahir: '1990-08-20',
    },
    targetData: {
      targetBiaya: 50000000,
      tanggalTarget: '2027-06-30',
      paketHaji: 'plus' as const,
    },
    tabunganData: {
      totalTabungan: 0,
      riwayatSetoran: [],
      lastUpdated: new Date().toISOString(),
    },
  },
  activeUser: {
    userData: DUMMY_USER_DATA,
    targetData: DUMMY_TARGET_DATA,
    tabunganData: DUMMY_TABUNGAN_DATA,
  },
  nearTargetUser: {
    userData: {
      nama: 'Muhammad Rizki',
      email: '<EMAIL>',
      telepon: '083456789012',
      alamat: 'Jl. Gatot Subroto No. 789, Bandung',
      tanggalLahir: '1988-03-10',
    },
    targetData: {
      targetBiaya: 35000000,
      tanggalTarget: '2025-12-31',
      paketHaji: 'reguler' as const,
    },
    tabunganData: {
      totalTabungan: 32000000, // 91% of target
      riwayatSetoran: DUMMY_SETORAN_HISTORY,
      lastUpdated: new Date().toISOString(),
    },
  },
};
