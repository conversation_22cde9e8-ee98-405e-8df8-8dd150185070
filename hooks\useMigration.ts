import { useState, useEffect } from 'react';
import { 
  migrateToSupabase, 
  isMigrationCompleted, 
  getMigrationStatus, 
  clearAsyncStorageAfterMigration 
} from '@/utils/migration';

interface MigrationState {
  isChecking: boolean;
  isCompleted: boolean;
  isMigrating: boolean;
  error: string | null;
  migrationDetails: {
    user: boolean;
    target: boolean;
    tabungan: boolean;
    settings: boolean;
  } | null;
}

export function useMigration() {
  const [state, setState] = useState<MigrationState>({
    isChecking: true,
    isCompleted: false,
    isMigrating: false,
    error: null,
    migrationDetails: null,
  });

  // Check migration status on mount
  useEffect(() => {
    checkMigrationStatus();
  }, []);

  const checkMigrationStatus = async () => {
    try {
      setState(prev => ({ ...prev, isChecking: true, error: null }));
      
      const completed = await isMigrationCompleted();
      const status = await getMigrationStatus();
      
      setState(prev => ({
        ...prev,
        isChecking: false,
        isCompleted: completed,
        migrationDetails: status?.migratedData || null,
      }));
      
    } catch (error) {
      setState(prev => ({
        ...prev,
        isChecking: false,
        error: error instanceof Error ? error.message : 'Failed to check migration status',
      }));
    }
  };

  const runMigration = async (): Promise<boolean> => {
    try {
      setState(prev => ({ ...prev, isMigrating: true, error: null }));
      
      const result = await migrateToSupabase();
      
      setState(prev => ({
        ...prev,
        isMigrating: false,
        isCompleted: result.success,
        migrationDetails: result.details,
        error: result.success ? null : result.message,
      }));
      
      return result.success;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Migration failed';
      setState(prev => ({
        ...prev,
        isMigrating: false,
        error: errorMessage,
      }));
      return false;
    }
  };

  const cleanupAsyncStorage = async (): Promise<boolean> => {
    try {
      await clearAsyncStorageAfterMigration();
      return true;
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to cleanup AsyncStorage',
      }));
      return false;
    }
  };

  // Auto-migrate if needed (can be called in app initialization)
  const autoMigrate = async (): Promise<boolean> => {
    if (state.isCompleted || state.isMigrating) {
      return state.isCompleted;
    }
    
    return await runMigration();
  };

  return {
    // State
    isChecking: state.isChecking,
    isCompleted: state.isCompleted,
    isMigrating: state.isMigrating,
    error: state.error,
    migrationDetails: state.migrationDetails,
    
    // Actions
    checkMigrationStatus,
    runMigration,
    cleanupAsyncStorage,
    autoMigrate,
    
    // Computed values
    needsMigration: !state.isCompleted && !state.isChecking,
    isReady: !state.isChecking && (state.isCompleted || state.error !== null),
  };
}
