// Demo script to showcase app functionality
import { 
  DUMMY_USER_DATA, 
  DUMMY_TARGET_DATA, 
  DUMMY_TABUNGAN_DATA,
  TEST_SCENARIOS,
  BIAYA_HAJI_2024,
  TIPS_MENABUNG_HAJI,
  MOTIVATIONAL_QUOTES 
} from '@/data/dummyData';
import { formatRupiah, formatDate, formatPercentage } from '@/utils/formatters';

export const runDemo = () => {
  console.log('🎯 DEMO: Aplikasi Tabungan Haji\n');
  console.log('=' .repeat(50));

  // Demo 1: User Profile
  console.log('\n👤 PROFIL PENGGUNA');
  console.log('-'.repeat(30));
  console.log(`Nama: ${DUMMY_USER_DATA.nama}`);
  console.log(`Email: ${DUMMY_USER_DATA.email}`);
  console.log(`Telepon: ${DUMMY_USER_DATA.telepon}`);
  console.log(`Alamat: ${DUMMY_USER_DATA.alamat}`);
  console.log(`Tanggal Lahir: ${formatDate(DUMMY_USER_DATA.tanggalLahir)}`);

  // Demo 2: Target Haji
  console.log('\n🎯 TARGET HAJI');
  console.log('-'.repeat(30));
  console.log(`Target Biaya: ${formatRupiah(DUMMY_TARGET_DATA.targetBiaya)}`);
  console.log(`Target Tanggal: ${formatDate(DUMMY_TARGET_DATA.tanggalTarget)}`);
  console.log(`Paket Haji: ${DUMMY_TARGET_DATA.paketHaji.toUpperCase()}`);

  // Demo 3: Progress Tabungan
  console.log('\n💰 PROGRESS TABUNGAN');
  console.log('-'.repeat(30));
  const progress = (DUMMY_TABUNGAN_DATA.totalTabungan / DUMMY_TARGET_DATA.targetBiaya) * 100;
  const remaining = DUMMY_TARGET_DATA.targetBiaya - DUMMY_TABUNGAN_DATA.totalTabungan;
  
  console.log(`Total Tabungan: ${formatRupiah(DUMMY_TABUNGAN_DATA.totalTabungan)}`);
  console.log(`Progress: ${formatPercentage(progress)}`);
  console.log(`Sisa Kebutuhan: ${formatRupiah(remaining)}`);
  console.log(`Jumlah Setoran: ${DUMMY_TABUNGAN_DATA.riwayatSetoran.length} kali`);

  // Demo 4: Riwayat Setoran (5 terakhir)
  console.log('\n📊 RIWAYAT SETORAN (5 TERAKHIR)');
  console.log('-'.repeat(30));
  DUMMY_TABUNGAN_DATA.riwayatSetoran.slice(0, 5).forEach((setoran, index) => {
    console.log(`${index + 1}. ${formatDate(setoran.tanggal)} - ${formatRupiah(setoran.jumlah)} (${setoran.keterangan})`);
  });

  // Demo 5: Paket Haji Available
  console.log('\n📦 PAKET HAJI TERSEDIA');
  console.log('-'.repeat(30));
  Object.entries(BIAYA_HAJI_2024).forEach(([key, paket]) => {
    console.log(`${paket.nama}: ${formatRupiah(paket.biaya)}`);
    console.log(`  ${paket.deskripsi}`);
    console.log(`  Fasilitas: ${paket.fasilitas.length} item\n`);
  });

  // Demo 6: Test Scenarios
  console.log('\n🧪 SKENARIO TESTING');
  console.log('-'.repeat(30));
  Object.entries(TEST_SCENARIOS).forEach(([scenarioName, scenario]) => {
    const progress = (scenario.tabunganData.totalTabungan / scenario.targetData.targetBiaya) * 100;
    console.log(`${scenarioName.toUpperCase()}:`);
    console.log(`  User: ${scenario.userData.nama}`);
    console.log(`  Target: ${formatRupiah(scenario.targetData.targetBiaya)}`);
    console.log(`  Tabungan: ${formatRupiah(scenario.tabunganData.totalTabungan)}`);
    console.log(`  Progress: ${formatPercentage(progress)}\n`);
  });

  // Demo 7: Tips Menabung
  console.log('\n💡 TIPS MENABUNG HAJI');
  console.log('-'.repeat(30));
  TIPS_MENABUNG_HAJI.forEach((kategori, index) => {
    console.log(`${index + 1}. ${kategori.kategori}:`);
    kategori.tips.forEach((tip, tipIndex) => {
      console.log(`   ${String.fromCharCode(97 + tipIndex)}. ${tip}`);
    });
    console.log('');
  });

  // Demo 8: Motivational Quotes
  console.log('\n🌟 MOTIVASI');
  console.log('-'.repeat(30));
  MOTIVATIONAL_QUOTES.forEach((quote, index) => {
    console.log(`${index + 1}. "${quote}"`);
  });

  console.log('\n' + '='.repeat(50));
  console.log('✨ Demo selesai! Aplikasi siap digunakan.');
};

// Performance metrics
export const showPerformanceMetrics = () => {
  console.log('\n📈 METRIK PERFORMA');
  console.log('-'.repeat(30));
  
  const startTime = Date.now();
  
  // Simulate data processing
  const processedData = {
    totalUsers: 1,
    totalSetoran: DUMMY_TABUNGAN_DATA.riwayatSetoran.length,
    totalAmount: DUMMY_TABUNGAN_DATA.totalTabungan,
    averageSetoran: DUMMY_TABUNGAN_DATA.totalTabungan / DUMMY_TABUNGAN_DATA.riwayatSetoran.length,
    dataSize: JSON.stringify(DUMMY_TABUNGAN_DATA).length,
  };
  
  const endTime = Date.now();
  const processingTime = endTime - startTime;
  
  console.log(`Processing Time: ${processingTime}ms`);
  console.log(`Data Size: ${processedData.dataSize} bytes`);
  console.log(`Total Setoran Records: ${processedData.totalSetoran}`);
  console.log(`Average Setoran: ${formatRupiah(processedData.averageSetoran)}`);
  console.log(`Memory Usage: ${(process.memoryUsage().heapUsed / 1024 / 1024).toFixed(2)} MB`);
};

// Feature checklist
export const showFeatureChecklist = () => {
  console.log('\n✅ CHECKLIST FITUR');
  console.log('-'.repeat(30));
  
  const features = [
    { name: 'Dashboard dengan progress tabungan', status: '✅' },
    { name: 'Input setoran baru', status: '✅' },
    { name: 'Riwayat setoran', status: '✅' },
    { name: 'Simulasi target haji', status: '✅' },
    { name: 'Kalkulator tabungan bulanan', status: '✅' },
    { name: 'Informasi biaya haji', status: '✅' },
    { name: 'Syarat dan rukun haji', status: '✅' },
    { name: 'Link ke portal Kemenag', status: '✅' },
    { name: 'Manajemen profil user', status: '✅' },
    { name: 'Edit target haji', status: '✅' },
    { name: 'Penyimpanan data lokal', status: '✅' },
    { name: 'UI/UX responsif', status: '✅' },
    { name: 'Dark/Light theme', status: '✅' },
    { name: 'Komponen UI reusable', status: '✅' },
    { name: 'Validasi input', status: '✅' },
    { name: 'Error handling', status: '✅' },
    { name: 'Loading states', status: '✅' },
    { name: 'Empty states', status: '✅' },
    { name: 'Toast notifications', status: '✅' },
    { name: 'Dummy data untuk testing', status: '✅' },
  ];
  
  features.forEach(feature => {
    console.log(`${feature.status} ${feature.name}`);
  });
  
  const completedFeatures = features.filter(f => f.status === '✅').length;
  const totalFeatures = features.length;
  const completionRate = (completedFeatures / totalFeatures) * 100;
  
  console.log(`\n📊 Completion Rate: ${completionRate}% (${completedFeatures}/${totalFeatures})`);
};

// Run complete demo
export const runCompleteDemo = () => {
  runDemo();
  showPerformanceMetrics();
  showFeatureChecklist();
};
