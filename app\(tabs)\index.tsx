import { AnimatedProgressBar } from '@/components/ui/AnimatedProgressBar';
import { AnimatedTouchable } from '@/components/ui/AnimatedTouchable';
import { FadeInView } from '@/components/ui/FadeInView';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import React, { useState } from 'react';
import { Alert, Dimensions, ScrollView, StyleSheet, Text, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

// Get screen dimensions
const { width: screenWidth } = Dimensions.get('window');

// Dummy data untuk simulasi
const DUMMY_DATA = {
  targetBiaya: 35000000, // 35 juta
  totalTabungan: 12500000, // 12.5 juta
  setoranTerakhir: 500000, // 500 ribu
  tanggalSetoranTerakhir: '2024-06-20',
  riwayatSetoran: [
    { id: 1, tanggal: '2024-06-20', jumlah: 500000 },
    { id: 2, tanggal: '2024-06-15', jumlah: 750000 },
    { id: 3, tanggal: '2024-06-10', jumlah: 600000 },
  ]
};

export default function BerandaScreen() {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];

  const [data, setData] = useState(DUMMY_DATA);

  // Hitung progress persentase
  const progressPercentage = (data.totalTabungan / data.targetBiaya) * 100;

  // Estimasi waktu tercapai (asumsi setoran rata-rata per bulan)
  const rataRataSetoranPerBulan = 600000; // dari dummy data
  const sisaTabungan = data.targetBiaya - data.totalTabungan;
  const estimasiBulan = Math.ceil(sisaTabungan / rataRataSetoranPerBulan);

  // Calculate remaining amount needed
  const sisaAmount = data.targetBiaya - data.totalTabungan;
  
  const formatRupiah = (amount: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const handleTambahSetoran = () => {
    Alert.alert(
      '💰 Tambah Setoran',
      'Untuk menambah setoran baru:\n\n1. Buka tab "Tabungan" di menu bawah\n2. Tekan tombol "Tambah Setoran"\n3. Masukkan jumlah setoran Anda\n\nSetiap setoran membawa Anda lebih dekat ke Tanah Suci! 🕌',
      [
        {
          text: 'Mengerti',
          style: 'default'
        }
      ]
    );
  };

  const handleSimulasi = () => {
    Alert.alert(
      '📊 Simulasi Tabungan',
      'Fitur simulasi akan membantu Anda:\n\n• Menghitung berapa lama mencapai target\n• Melihat proyeksi tabungan bulanan\n• Mengatur target setoran yang realistis\n\nFitur ini akan segera tersedia!',
      [{ text: 'OK' }]
    );
  };

  const handleInfoHaji = () => {
    Alert.alert(
      '📖 Informasi Haji',
      'Dapatkan informasi lengkap tentang:\n\n• Syarat dan rukun haji\n• Persiapan ibadah haji\n• Tips dan panduan perjalanan\n• Doa-doa haji\n\nFitur ini akan segera tersedia!',
      [{ text: 'OK' }]
    );
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {/* Modern Header */}
        <FadeInView delay={0} duration={500}>
          <View style={styles.header}>
            <View style={styles.headerContent}>
              <Text style={[styles.greeting, { color: colors.textSecondary }]}>
                Assalamu'alaikum
              </Text>
              <Text style={[styles.title, { color: colors.text }]}>
                Tabungan Haji Anda
              </Text>
            </View>
            <AnimatedTouchable style={[styles.headerIcon, { backgroundColor: colors.primary + '15' }]}>
              <IconSymbol name="person.circle.fill" size={32} color={colors.primary} />
            </AnimatedTouchable>
          </View>
        </FadeInView>

        {/* Hero Progress Card */}
        <FadeInView delay={200} duration={600}>
          <View style={[styles.heroCard, { backgroundColor: colors.primary }]}>
            <View style={styles.heroHeader}>
              <View style={styles.heroIconContainer}>
                <IconSymbol name="target" size={28} color="white" />
              </View>
              <View style={styles.heroTextContainer}>
                <Text style={styles.heroTitle}>Progress Tabungan</Text>
                <Text style={styles.heroSubtitle}>Menuju Tanah Suci</Text>
              </View>
            </View>

            <View style={styles.heroAmountContainer}>
              <Text style={styles.heroAmount}>
                {formatRupiah(data.totalTabungan)}
              </Text>
              <Text style={styles.heroTarget}>
                dari {formatRupiah(data.targetBiaya)}
              </Text>
            </View>

            {/* Enhanced Progress Bar */}
            <View style={styles.progressSection}>
              <AnimatedProgressBar
                progress={progressPercentage}
                height={12}
                backgroundColor="rgba(255,255,255,0.25)"
                progressColor="white"
                borderRadius={6}
                animationDelay={800}
                animationDuration={1200}
                style={styles.progressBarContainer}
              />
              <View style={styles.progressInfo}>
                <Text style={styles.progressPercentage}>
                  {progressPercentage.toFixed(1)}% tercapai
                </Text>
                <Text style={styles.progressRemaining}>
                  Sisa: {formatRupiah(sisaAmount)}
                </Text>
              </View>
            </View>
          </View>
        </FadeInView>

        {/* Enhanced Stats Grid */}
        <FadeInView delay={400} duration={600}>
          <View style={styles.statsGrid}>
            <View style={[styles.statCard, { backgroundColor: colors.cardBackground }]}>
              <View style={[styles.statIconContainer, { backgroundColor: colors.info + '15' }]}>
                <IconSymbol name="calendar" size={20} color={colors.info} />
              </View>
              <View style={styles.statContent}>
                <Text style={[styles.statLabel, { color: colors.textSecondary }]}>
                  Estimasi Tercapai
                </Text>
                <Text style={[styles.statValue, { color: colors.text }]}>
                  {estimasiBulan} bulan
                </Text>
              </View>
            </View>

            <View style={[styles.statCard, { backgroundColor: colors.cardBackground }]}>
              <View style={[styles.statIconContainer, { backgroundColor: colors.success + '15' }]}>
                <IconSymbol name="chart.bar.fill" size={20} color={colors.success} />
              </View>
              <View style={styles.statContent}>
                <Text style={[styles.statLabel, { color: colors.textSecondary }]}>
                  Setoran Terakhir
                </Text>
                <Text style={[styles.statValue, { color: colors.text }]}>
                  {formatRupiah(data.setoranTerakhir)}
                </Text>
              </View>
            </View>
          </View>
        </FadeInView>

        {/* Quick Actions */}
        <FadeInView delay={600} duration={600}>
          <View style={styles.actionsContainer}>
            <AnimatedTouchable
              style={[styles.primaryAction, { backgroundColor: colors.primary }]}
              onPress={handleTambahSetoran}
              scaleValue={0.95}
            >
              <IconSymbol name="plus.circle.fill" size={24} color="white" />
              <Text style={styles.primaryActionText}>Tambah Setoran</Text>
            </AnimatedTouchable>

            <View style={styles.secondaryActions}>
              <AnimatedTouchable
                style={[styles.secondaryAction, { backgroundColor: colors.cardBackground }]}
                scaleValue={0.96}
                onPress={handleSimulasi}
              >
                <IconSymbol name="chart.line.uptrend.xyaxis" size={20} color={colors.primary} />
                <Text style={[styles.secondaryActionText, { color: colors.text }]}>Simulasi</Text>
              </AnimatedTouchable>

              <AnimatedTouchable
                style={[styles.secondaryAction, { backgroundColor: colors.cardBackground }]}
                scaleValue={0.96}
                onPress={handleInfoHaji}
              >
                <IconSymbol name="doc.text" size={20} color={colors.primary} />
                <Text style={[styles.secondaryActionText, { color: colors.text }]}>Info Haji</Text>
              </AnimatedTouchable>
            </View>
          </View>
        </FadeInView>

        {/* Motivational Section */}
        <FadeInView delay={800} duration={600}>
          <View style={[styles.motivationSection, { backgroundColor: colors.cardBackground }]}>
            <View style={styles.motivationHeader}>
              <Text style={[styles.motivationTitle, { color: colors.text }]}>
                💪 Tetap Semangat!
              </Text>
              <Text style={[styles.motivationSubtitle, { color: colors.textSecondary }]}>
                Perjalanan menuju Tanah Suci
              </Text>
            </View>
            <Text style={[styles.motivationText, { color: colors.textSecondary }]}>
              Anda sudah menabung {progressPercentage.toFixed(1)}% dari target.
              Konsisten menabung akan membawa Anda lebih dekat ke Tanah Suci!
            </Text>
          </View>
        </FadeInView>

        {/* Recent Transactions */}
        <FadeInView delay={1000} duration={600}>
          <View style={[styles.transactionsCard, { backgroundColor: colors.cardBackground }]}>
            <View style={styles.transactionsHeader}>
              <Text style={[styles.transactionsTitle, { color: colors.text }]}>
                Setoran Terbaru
              </Text>
              <AnimatedTouchable scaleValue={0.95}>
                <Text style={[styles.viewAllText, { color: colors.primary }]}>
                  Lihat Semua
                </Text>
              </AnimatedTouchable>
            </View>

            {data.riwayatSetoran.slice(0, 3).map((item, index) => (
              <FadeInView
                key={item.id}
                delay={1200 + (index * 100)}
                duration={400}
              >
                <AnimatedTouchable
                  style={[
                    styles.transactionItem,
                    index < 2 && { borderBottomWidth: 1, borderBottomColor: colors.border }
                  ]}
                  scaleValue={0.98}
                >
                  <View style={styles.transactionLeft}>
                    <View style={[styles.transactionIcon, { backgroundColor: colors.success + '15' }]}>
                      <IconSymbol name="arrow.up.circle.fill" size={16} color={colors.success} />
                    </View>
                    <View style={styles.transactionDetails}>
                      <Text style={[styles.transactionType, { color: colors.text }]}>
                        Setoran Tabungan
                      </Text>
                      <Text style={[styles.transactionDate, { color: colors.textSecondary }]}>
                        {new Date(item.tanggal).toLocaleDateString('id-ID', {
                          day: 'numeric',
                          month: 'short',
                          year: 'numeric'
                        })}
                      </Text>
                    </View>
                  </View>
                  <Text style={[styles.transactionAmount, { color: colors.success }]}>
                    +{formatRupiah(item.jumlah)}
                  </Text>
                </AnimatedTouchable>
              </FadeInView>
            ))}
          </View>
        </FadeInView>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 24,
  },

  // Header Styles - Improved spacing and responsiveness
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 8,
    paddingBottom: 16,
    marginBottom: 12,
  },
  headerContent: {
    flex: 1,
    marginRight: 16,
  },
  greeting: {
    fontSize: 14,
    fontWeight: '400',
    marginBottom: 6,
    opacity: 0.8,
  },
  title: {
    fontSize: Math.min(28, screenWidth * 0.07),
    fontWeight: '700',
    lineHeight: Math.min(36, screenWidth * 0.09),
  },
  headerIcon: {
    width: 52,
    height: 52,
    borderRadius: 26,
    alignItems: 'center',
    justifyContent: 'center',
  },

  // Hero Card Styles - Enhanced for better visual hierarchy
  heroCard: {
    marginHorizontal: 20,
    marginBottom: 28,
    padding: Math.max(20, screenWidth * 0.06),
    borderRadius: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 12 },
    shadowOpacity: 0.2,
    shadowRadius: 20,
    elevation: 12,
  },
  heroHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 24,
  },
  heroIconContainer: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: 'rgba(255,255,255,0.2)',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  heroTextContainer: {
    flex: 1,
  },
  heroTitle: {
    color: 'white',
    fontSize: 20,
    fontWeight: '700',
    marginBottom: 4,
    letterSpacing: 0.3,
  },
  heroSubtitle: {
    color: 'rgba(255,255,255,0.85)',
    fontSize: 15,
    fontWeight: '500',
  },
  heroAmountContainer: {
    marginBottom: 28,
    alignItems: 'flex-start',
  },
  heroAmount: {
    color: 'white',
    fontSize: Math.min(36, screenWidth * 0.09),
    fontWeight: '800',
    lineHeight: Math.min(44, screenWidth * 0.11),
    marginBottom: 6,
    letterSpacing: -0.5,
  },
  heroTarget: {
    color: 'rgba(255,255,255,0.9)',
    fontSize: 17,
    fontWeight: '500',
  },

  // Progress Section Styles
  progressSection: {
    marginTop: 8,
  },
  progressBarContainer: {
    height: 12,
    backgroundColor: 'rgba(255,255,255,0.25)',
    borderRadius: 6,
    marginBottom: 16,
    overflow: 'hidden',
  },
  progressBar: {
    height: '100%',
    backgroundColor: 'white',
    borderRadius: 6,
  },
  progressInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  progressPercentage: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  progressRemaining: {
    color: 'rgba(255,255,255,0.8)',
    fontSize: 14,
    fontWeight: '400',
  },

  // Stats Grid Styles - Compact design for better space usage
  statsGrid: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    marginBottom: 24,
    gap: 12,
  },
  statCard: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    padding: 14,
    borderRadius: 14,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.06,
    shadowRadius: 8,
    elevation: 2,
    minHeight: 68,
  },
  statIconContainer: {
    width: 42,
    height: 42,
    borderRadius: 21,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 10,
  },
  statContent: {
    flex: 1,
    justifyContent: 'center',
  },
  statLabel: {
    fontSize: 10,
    fontWeight: '600',
    marginBottom: 4,
    textTransform: 'uppercase',
    letterSpacing: 0.6,
    opacity: 0.75,
  },
  statValue: {
    fontSize: 15,
    fontWeight: '700',
    lineHeight: 19,
    letterSpacing: -0.1,
  },

  // Actions Styles - Enhanced button design with better prominence
  actionsContainer: {
    paddingHorizontal: 20,
    marginBottom: 28,
  },
  primaryAction: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 20,
    paddingHorizontal: 32,
    borderRadius: 20,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.2,
    shadowRadius: 16,
    elevation: 8,
    minHeight: 60,
    // Add a subtle border for extra definition
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.1)',
  },
  primaryActionText: {
    color: 'white',
    fontSize: 18,
    fontWeight: '700',
    marginLeft: 12,
    letterSpacing: 0.3,
    textShadowColor: 'rgba(0,0,0,0.2)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  secondaryActions: {
    flexDirection: 'row',
    gap: 14,
  },
  secondaryAction: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 14,
    paddingHorizontal: 18,
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 2,
    minHeight: 52,
  },
  secondaryActionText: {
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 8,
    letterSpacing: 0.1,
  },

  // Motivation Section Styles - Enhanced visual appeal
  motivationSection: {
    marginHorizontal: 20,
    marginBottom: 28,
    padding: 24,
    borderRadius: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 4,
  },
  motivationHeader: {
    marginBottom: 16,
  },
  motivationTitle: {
    fontSize: 20,
    fontWeight: '700',
    marginBottom: 6,
    letterSpacing: 0.2,
  },
  motivationSubtitle: {
    fontSize: 15,
    fontWeight: '600',
    opacity: 0.75,
  },
  motivationText: {
    fontSize: 15,
    lineHeight: 24,
    fontWeight: '400',
    letterSpacing: 0.1,
  },

  // Transactions Card Styles - Improved spacing and hierarchy
  transactionsCard: {
    marginHorizontal: 20,
    marginBottom: 32,
    borderRadius: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 4,
  },
  transactionsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 16,
  },
  transactionsTitle: {
    fontSize: 18,
    fontWeight: '700',
  },
  viewAllText: {
    fontSize: 14,
    fontWeight: '600',
  },
  transactionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 24,
    paddingVertical: 18,
    minHeight: 72,
  },
  transactionLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  transactionIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  transactionDetails: {
    flex: 1,
    justifyContent: 'center',
  },
  transactionType: {
    fontSize: 15,
    fontWeight: '600',
    marginBottom: 4,
    letterSpacing: 0.1,
  },
  transactionDate: {
    fontSize: 13,
    fontWeight: '500',
    opacity: 0.8,
  },
  transactionAmount: {
    fontSize: 17,
    fontWeight: '700',
    letterSpacing: -0.2,
  },

});
