import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import React from 'react';
import {
  Alert,
  Linking,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

// Data informasi haji
const SYARAT_HAJI = [
  'Beragama Islam',
  'Baligh (dewasa) dan berakal sehat',
  'Mampu secara finansial (istitha\'ah)',
  'Mampu secara fisik dan kesehatan',
  'Tidak memiliki hutang yang mendesak',
];

const RUKUN_HAJI = [
  'Ihram',
  '<PERSON>ku<PERSON> di <PERSON>h',
  '<PERSON>wa<PERSON>',
  'Sa\'i antara Shafa dan Marwah',
];

const BIAYA_ESTIMASI = [
  { jenis: 'Haji Reguler', biaya: 'Rp 35.000.000', keterangan: 'Paket standar dengan fasilitas dasar' },
  { jenis: 'Haji Plus', biaya: 'Rp 50.000.000', keterangan: 'Fasilitas lebih baik dan nyaman' },
  { jenis: 'Haji Khusus', biaya: 'Rp 75.000.000', keterangan: 'Fasilitas premium dan eksklusif' },
];

const TIPS_PERSIAPAN = [
  {
    title: 'Persiapan Finansial',
    items: [
      'Mulai menabung sedini mungkin',
      'Buat target tabungan yang realistis',
      'Manfaatkan investasi syariah',
      'Siapkan dana darurat tambahan'
    ]
  },
  {
    title: 'Persiapan Fisik',
    items: [
      'Jaga kesehatan dan stamina',
      'Lakukan medical check-up rutin',
      'Latihan berjalan jarak jauh',
      'Konsumsi makanan bergizi'
    ]
  },
  {
    title: 'Persiapan Mental & Spiritual',
    items: [
      'Pelajari manasik haji',
      'Tingkatkan ibadah dan dzikir',
      'Mohon doa dari keluarga',
      'Niatkan haji karena Allah SWT'
    ]
  }
];

export default function InfoHajiScreen() {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];

  const handleOpenKemenag = async () => {
    const url = 'https://haji.kemenag.go.id/';
    const supported = await Linking.canOpenURL(url);
    
    if (supported) {
      await Linking.openURL(url);
    } else {
      Alert.alert('Error', 'Tidak dapat membuka link. Silakan buka browser dan kunjungi haji.kemenag.go.id');
    }
  };

  const handleOpenSiskohat = async () => {
    const url = 'https://siskohat.kemenag.go.id/';
    const supported = await Linking.canOpenURL(url);
    
    if (supported) {
      await Linking.openURL(url);
    } else {
      Alert.alert('Error', 'Tidak dapat membuka link. Silakan buka browser dan kunjungi siskohat.kemenag.go.id');
    }
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={[styles.title, { color: colors.text }]}>
            Informasi Haji
          </Text>
          <Text style={[styles.subtitle, { color: colors.text }]}>
            Panduan lengkap persiapan haji
          </Text>
        </View>

        {/* Syarat Haji */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            📋 Syarat Wajib Haji
          </Text>
          <View style={[styles.card, { backgroundColor: colors.cardBackground }]}>
            {SYARAT_HAJI.map((syarat, index) => (
              <View key={index} style={styles.listItem}>
                <View style={[styles.checkmarkIcon, { backgroundColor: colors.backgroundTertiary }]}>
                  <IconSymbol name="checkmark" size={12} color={colors.primary} />
                </View>
                <Text style={[styles.listText, { color: colors.text }]}>
                  {syarat}
                </Text>
              </View>
            ))}
          </View>
        </View>

        {/* Rukun Haji */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            🕋 Rukun Haji
          </Text>
          <View style={[styles.card, { backgroundColor: colors.cardBackground }]}>
            {RUKUN_HAJI.map((rukun, index) => (
              <View key={index} style={styles.listItem}>
                <View style={[styles.numberBadge, { backgroundColor: colors.primary }]}>
                  <Text style={styles.numberText}>{index + 1}</Text>
                </View>
                <Text style={[styles.listText, { color: colors.text }]}>
                  {rukun}
                </Text>
              </View>
            ))}
          </View>
        </View>

        {/* Estimasi Biaya */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            💰 Estimasi Biaya Haji 2024
          </Text>
          {BIAYA_ESTIMASI.map((item, index) => (
            <View key={index} style={[
              styles.biayaCard,
              {
                backgroundColor: colors.cardBackground,
                borderWidth: 1,
                borderColor: 'rgba(163, 183, 202, 0.2)'
              }
            ]}>
              <View style={styles.biayaHeader}>
                <Text style={[styles.biayaJenis, { color: colors.text }]}>
                  {item.jenis}
                </Text>
                <Text style={[styles.biayaAmount, { color: colors.primary }]}>
                  {item.biaya}
                </Text>
              </View>
              <Text style={[styles.biayaKeterangan, { color: colors.textSecondary }]}>
                {item.keterangan}
              </Text>
            </View>
          ))}
        </View>

        {/* Tips Persiapan */}
        <View style={styles.section}>
          <View style={styles.tipsHeader}>
            <View style={[styles.tipsIconContainer, { backgroundColor: colors.backgroundTertiary }]}>
              <IconSymbol name="lightbulb" size={20} color={colors.primary} />
            </View>
            <Text style={[styles.sectionTitle, { color: colors.text, marginBottom: 0, flex: 1 }]}>
              Tips Persiapan Haji
            </Text>
          </View>
          {TIPS_PERSIAPAN.map((tip, index) => (
            <View key={index} style={[styles.card, { backgroundColor: colors.cardBackground }]}>
              <Text style={[styles.tipTitle, { color: colors.primary }]}>
                {tip.title}
              </Text>
              {tip.items.map((item, itemIndex) => (
                <View key={itemIndex} style={styles.listItem}>
                  <View style={[styles.checkmarkIcon, { backgroundColor: colors.backgroundTertiary }]}>
                    <IconSymbol name="checkmark" size={12} color={colors.primary} />
                  </View>
                  <Text style={[styles.listText, { color: colors.text }]}>
                    {item}
                  </Text>
                </View>
              ))}
            </View>
          ))}
        </View>

        {/* Link Kemenag */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            🔗 Link Penting
          </Text>
          
          <TouchableOpacity
            style={[styles.linkCard, { backgroundColor: colors.primary }]}
            onPress={handleOpenKemenag}
          >
            <View style={styles.linkContent}>
              <IconSymbol name="info.circle.fill" size={24} color="white" />
              <View style={styles.linkText}>
                <Text style={styles.linkTitle}>Portal Haji Kemenag</Text>
                <Text style={styles.linkSubtitle}>
                  Informasi resmi dan pendaftaran haji
                </Text>
              </View>
            </View>
            <IconSymbol name="chevron.right" size={20} color="white" />
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.linkCard, { backgroundColor: colors.cardBackground }]}
            onPress={handleOpenSiskohat}
          >
            <View style={styles.linkContent}>
              <IconSymbol name="calendar" size={24} color={colors.primary} />
              <View style={styles.linkText}>
                <Text style={[styles.linkTitle, { color: colors.text }]}>
                  Siskohat Kemenag
                </Text>
                <Text style={[styles.linkSubtitle, { color: colors.textSecondary }]}>
                  Sistem informasi dan komputerisasi haji terpadu
                </Text>
              </View>
            </View>
            <IconSymbol name="chevron.right" size={20} color={colors.primary} />
          </TouchableOpacity>
        </View>

        {/* Doa */}
        <View style={styles.section}>
          <View style={[styles.doaCard, { backgroundColor: colors.primary }]}>
            <Text style={styles.doaTitle}>🤲 Doa Niat Haji</Text>
            <Text style={styles.doaArabic}>
              لَبَّيْكَ اللَّهُمَّ حَجًّا
            </Text>
            <Text style={styles.doaTranslation}>
              "Labbaika Allahumma hajjan"
            </Text>
            <Text style={styles.doaArtinya}>
              Artinya: "Aku penuhi panggilan-Mu ya Allah untuk berhaji"
            </Text>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: 20,
    paddingBottom: 10,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  subtitle: {
    fontSize: 14,
    opacity: 0.7,
    marginTop: 4,
  },
  section: {
    padding: 20,
    paddingTop: 10,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  card: {
    padding: 16,
    borderRadius: 16,
    marginBottom: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 4,
    borderWidth: 1,
    borderColor: 'rgba(163, 183, 202, 0.15)',
  },
  listItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  bullet: {
    fontSize: 16,
    marginRight: 8,
    marginTop: 2,
  },
  listText: {
    fontSize: 14,
    flex: 1,
    lineHeight: 20,
  },
  numberBadge: {
    width: 20,
    height: 20,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
    marginTop: 2,
  },
  numberText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  checkmarkIcon: {
    width: 18,
    height: 18,
    borderRadius: 9,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
    marginTop: 1,
  },
  tipsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  tipsIconContainer: {
    width: 32,
    height: 32,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  biayaCard: {
    padding: 16,
    borderRadius: 16,
    marginBottom: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 4,
  },
  biayaHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  biayaJenis: {
    fontSize: 16,
    fontWeight: '600',
  },
  biayaAmount: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  biayaKeterangan: {
    fontSize: 12,
    opacity: 0.7,
  },
  tipTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  linkCard: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderRadius: 16,
    marginBottom: 12,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  linkContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  linkText: {
    marginLeft: 12,
    flex: 1,
  },
  linkTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: 'white',
  },
  linkSubtitle: {
    fontSize: 12,
    color: 'white',
    opacity: 0.9,
    marginTop: 2,
  },
  doaCard: {
    padding: 20,
    borderRadius: 16,
    alignItems: 'center',
  },
  doaTitle: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 16,
  },
  doaArabic: {
    color: 'white',
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
  },
  doaTranslation: {
    color: 'white',
    fontSize: 14,
    fontStyle: 'italic',
    marginBottom: 8,
  },
  doaArtinya: {
    color: 'white',
    fontSize: 12,
    textAlign: 'center',
    opacity: 0.9,
  },
});
